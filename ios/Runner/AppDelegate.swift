import UIKit
import Flutter
import GoogleMaps
import FirebaseCore
import UserNotifications

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> <PERSON>ol {

    // Initialize Firebase
    FirebaseApp.configure()

    // Google Maps API Key
    GMSServices.provideAPIKey("AIzaSyCqidk982_mxHi5xA8pW37E9Yw7PHYtGaU")
    
    GeneratedPluginRegistrant.register(with: self)
    
    if #available(iOS 10.0, *) {
  UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
}
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
