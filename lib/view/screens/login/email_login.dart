import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rapsap/controllers/user_controller.dart';
import 'package:rapsap/view/screens/login/forgot_email.dart';
import 'package:rapsap/view/screens/login/register_screen.dart';
import 'package:rapsap/view/screens/login/widgets/button.dart';
import 'package:rapsap/view/widgets/keyboardhider.dart';
import 'package:rapsap/view/widgets/loadingscreen.dart';

import '../../../utils/constants.dart';

class EmailLogin extends StatelessWidget {
  EmailLogin({Key? key}) : super(key: key);
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final UserController _usrcontroller = Get.find<UserController>();

    final TextEditingController _emailController = TextEditingController();
    final TextEditingController _pwdController = TextEditingController();
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Log in',
          style: TextStyle(color: kblack, fontWeight: FontWeight.bold),
        ),
        foregroundColor: kblue,
        backgroundColor: Get.theme.scaffoldBackgroundColor,
      ),
      body: KeyboardHider(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  kheight20,
                  InputTextField(
                      hint: 'Enter Email',
                      label: 'Email',
                      txtcontroller: _emailController),
                  kheight10,
                  Obx(() => InputTextField(
                      hint: 'Enter Password',
                      label: 'Password',
                      obscure: _usrcontroller.showpassword.value ? true : false,
                      txtcontroller: _pwdController)),

                  //Forgot Password
                  // TextButton(
                  //     onPressed: () {
                  //       Get.to(() => ForgotPasswordScreen());
                  //     },
                  //     child: const Text(
                  //       'Forgot Password?',
                  //       style: TextStyle(
                  //           color: kblue, fontWeight: FontWeight.bold),
                  //     )),
                  kheight10,

                  SubmitButton(
                      text: 'Continue',
                      onpress: () {
                        if (_formKey.currentState!.validate()) {
                          Get.to(() => const LoadingScreen(
                                text: 'Loggin in',
                              ));
                          _usrcontroller.fetchUserWithEmail({
                            'email': _emailController.text,
                            'password': _pwdController.text
                          });
                          // _usrcontroller.loading.value = false;
                        }
                      })
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
