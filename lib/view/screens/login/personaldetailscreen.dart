import 'package:rapsap/view/screens/login/register_screen.dart';
import 'package:rapsap/view/screens/login/widgets/button.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:loading_indicator/loading_indicator.dart';

import '../../widgets/keyboardhider.dart';
import '../../widgets/loadingscreen.dart';

class PersonalDetailsScreen extends GetView<UserController> {
  PersonalDetailsScreen({Key? key, this.userid, this.phonenumber})
      : super(key: key);
  final int? userid;
  final String? phonenumber;
  final UserController usrcontroller = Get.find();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _pwdController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF1F2F2),
      appBar: AppBar(
        backgroundColor: const Color(0xffF1F2F2),
        toolbarHeight: 100,
        titleSpacing: defaultpadding,
        elevation: 0,
        title: const Text(
          'Personal Details',
          style: TextStyle(color: kblack, fontWeight: FontWeight.bold),
        ),
        automaticallyImplyLeading: false,
        foregroundColor: kblue,
      ),
      body: KeyboardHider(
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(defaultpadding),
                child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        kheight10,
                        FieldText(
                            hint: 'Enter Name',
                            label: 'Name',
                            txtcontroller: _nameController),
                        kheight10,
                        FieldText(
                            hint: 'Enter Email ',
                            label: 'Email',
                            txtcontroller: _emailController),
                        kheight30,
                        kheight20,
                      ],
                    )),
              ),
            ),
            Align(
                alignment: Alignment.bottomCenter,
                child: Obx(
                  () => SizedBox(
                    height: 56,
                    child: usrcontroller.buttonloading.value
                        ? const SizedBox(
                            height: 56,
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: null,
                              child: SizedBox(
                                height: 20,
                                child: LoadingIndicator(
                                  colors: [kblue, kblack],
                                  indicatorType: Indicator.cubeTransition,
                                ),
                              ),
                            ),
                          )
                        : SubmitButton(
                            text: 'Continue',
                            onpress: () {
                              if (_formKey.currentState!.validate()) {
                                if (_emailController.text.isNotEmpty) {
                                  controller.editProfile({
                                    "user_id": userid.toString(),
                                    "email": _emailController.text,
                                    "first_name": _nameController.text,
                                  });
                                } else {
                                  controller.editProfile({
                                    "user_id": userid.toString(),
                                    "first_name": _nameController.text,
                                  });
                                }
                              }
                            },
                          ),
                  ).paddingAll(defaultpadding),
                ))
          ],
        ),
      ),
    );
  }
}

class FieldText extends GetView<UserController> {
  const FieldText({
    Key? key,
    this.obscure = false,
    required this.label,
    required this.hint,
    required this.txtcontroller,
  }) : super(key: key);
  final String label;
  final String hint;
  final bool obscure;
  final TextEditingController txtcontroller;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
              text: label,
              style:
                  const TextStyle(fontWeight: FontWeight.bold, color: kblack),
              children: [
                TextSpan(
                  text: label == "Email" ? ' (Optional)' : "",
                  style: const TextStyle(
                      fontWeight: FontWeight.w400, color: kgrey),
                )
              ]),
        ),
        kheight10,
        SizedBox(
          child: TextFormField(
            controller: txtcontroller,
            obscureText: obscure,

            decoration: InputDecoration(
                hintStyle: const TextStyle(color: kblack),
                isDense: true,
                fillColor: kwhite,
                contentPadding: const EdgeInsets.all(14),
                filled: true,
                focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: kblack, width: 1.0),
                    borderRadius: BorderRadius.circular(2)),
                enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide.none,
                    borderRadius: BorderRadius.circular(2)),
                border: OutlineInputBorder(
                    borderSide: const BorderSide(color: kblack, width: 1.0),
                    borderRadius: BorderRadius.circular(2)),
                focusedErrorBorder: OutlineInputBorder(
                    borderSide:
                        BorderSide(color: Colors.red.shade300, width: 1),
                    borderRadius: BorderRadius.circular(2)),
                errorBorder: OutlineInputBorder(
                    borderSide:
                        BorderSide(color: Colors.red.shade300, width: 1),
                    borderRadius: BorderRadius.circular(2)),
                hintText: hint,
                suffix: label == 'Password'
                    ? GestureDetector(
                        onTap: () {
                          controller.showpassword.value =
                              !controller.showpassword.value;
                        },
                        child: Obx(() => Text(
                              controller.showpassword.value ? 'Show' : 'Hide',
                              style: const TextStyle(
                                  color: kblue, fontWeight: FontWeight.w500),
                            )))
                    : null),
            // The validator receives the text that the user has entered.
            validator: (value) {
              return label == "Email" && txtcontroller.text.isEmpty
                  ? null
                  : GetUtils.isBlank(value)!
                      ? 'cannot be empty'
                      : label == 'Email'
                          ? GetUtils.isEmail(value!)
                              ? null
                              : 'Enter valid email'
                          : label == 'Name'
                              ? value!.length < 4
                                  ? 'Enter valid name'
                                  : null
                              : label == 'Password'
                                  ? value!.length < 6
                                      ? 'Min 6 characters required'
                                      : null
                                  : null;
            },
          ),
        ),
        kheight10,
      ],
    );
  }
}
