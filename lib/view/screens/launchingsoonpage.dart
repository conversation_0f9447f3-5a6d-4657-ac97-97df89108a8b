import 'package:rapsap/main.dart';
import 'package:rapsap/services/orderservices.dart';
import 'package:rapsap/view/screens/login/widgets/button.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:rapsap/view/widgets/custom.dart';

import '../../controllers/accountscontroller.dart';

class LaunchingSoon extends StatelessWidget {
  const LaunchingSoon({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          SvgPicture.asset(
            'assets/svg/launchingsoonbg.svg',
            fit: BoxFit.fill,
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    'assets/svg/launchingsoonhanger.svg',
                  ),
                ],
              ),
              hgap(Get.height * 0.1),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset('assets/svg/launchingsoonbike.svg'),
                ],
              ),
              hgap(40),
              Text(
                'Online Delivery',
                style: GoogleFonts.barlowCondensed(
                    fontSize: 28, fontWeight: FontWeight.w700),
              ),
              Text(
                'Coming soon',
                style: GoogleFonts.barlowCondensed(
                    fontSize: 50, fontWeight: FontWeight.w700),
              ),
              hgap(15),
              Flexible(
                child: Text(
                  'We are working to get your groceries at your doorstep. Stay tuned with us.',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.dmSans(
                      color: const Color(0xffAFAFAF),
                      fontSize: 16,
                      fontWeight: FontWeight.w500),
                ),
              ),
              hgap(50),
              SizedBox(
                  height: 45,
                  child: SubmitButton(
                      textsize: 16,
                      text: 'Notify Me',
                      onpress: () async {
                        final UserController userController = Get.find();
                        await OrderServices.notifyLaunching({
                          "phone":
                              userController.userdata.value.data?.mobile ?? "",
                          "email":
                              userController.userdata.value.data?.email ?? "",
                          "pincode": storage.read('tempPincode') ?? "",
                          "latitude":
                              userController.locationpoint.value['latitude'] ??
                                  "",
                          "longitude":
                              userController.locationpoint.value['longitude'] ??
                                  "",
                          "user_id": userController.userdata.value.data?.id,
                          "name": userController.userdata.value.data?.firstName
                        });
                        final accountController = Get.find<AccountController>();
                        accountController.logoutUser();
                      })),
              hgap(20)
            ],
          ).paddingSymmetric(horizontal: 40),
        ],
      ),
    );
  }
}
