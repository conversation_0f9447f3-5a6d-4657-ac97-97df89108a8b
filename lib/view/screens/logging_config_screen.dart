import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rapsap/services/logging_config.dart';
import 'package:rapsap/services/logging_service.dart';
import 'package:rapsap/view/widgets/commons.dart';

/// Screen for managing logging configuration
class LoggingConfigScreen extends StatefulWidget {
  const LoggingConfigScreen({Key? key}) : super(key: key);

  @override
  State<LoggingConfigScreen> createState() => _LoggingConfigScreenState();
}

class _LoggingConfigScreenState extends State<LoggingConfigScreen> {
  final LoggingConfig _config = LoggingConfig.instance;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Logging Configuration'),
        backgroundColor: kblue,
        foregroundColor: kwhite,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('General Settings'),
            _buildSwitchTile(
              title: 'Enable Logging',
              subtitle: 'Turn logging on/off globally',
              value: _config.isEnabled,
              onChanged: (value) {
                setState(() {
                  _config.setEnabled(value);
                });
              },
            ),
            _buildLogLevelDropdown(),
            
            const SizedBox(height: 24),
            _buildSectionTitle('Logging Categories'),
            _buildSwitchTile(
              title: 'API Logging',
              subtitle: 'Log all API requests and responses',
              value: _config.isApiLoggingEnabled,
              onChanged: (value) {
                setState(() {
                  _config.setApiLoggingEnabled(value);
                });
              },
            ),
            _buildSwitchTile(
              title: 'Navigation Logging',
              subtitle: 'Log screen navigation events',
              value: _config.isNavigationLoggingEnabled,
              onChanged: (value) {
                setState(() {
                  _config.setNavigationLoggingEnabled(value);
                });
              },
            ),
            _buildSwitchTile(
              title: 'User Interaction Logging',
              subtitle: 'Log button clicks and user interactions',
              value: _config.isInteractionLoggingEnabled,
              onChanged: (value) {
                setState(() {
                  _config.setInteractionLoggingEnabled(value);
                });
              },
            ),
            
            const SizedBox(height: 24),
            _buildSectionTitle('Output Settings'),
            _buildSwitchTile(
              title: 'Console Output',
              subtitle: 'Show logs in debug console',
              value: _config.isConsoleOutputEnabled,
              onChanged: (value) {
                setState(() {
                  _config.setConsoleOutputEnabled(value);
                });
              },
            ),
            _buildSwitchTile(
              title: 'Performance Logging',
              subtitle: 'Log performance metrics',
              value: _config.isPerformanceLoggingEnabled,
              onChanged: (value) {
                setState(() {
                  _config.setPerformanceLoggingEnabled(value);
                });
              },
            ),
            
            const SizedBox(height: 32),
            _buildPresetButtons(),
            
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: kblue,
        ),
      ),
    );
  }
  
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: SwitchListTile(
        title: Text(title),
        subtitle: Text(subtitle),
        value: value,
        onChanged: onChanged,
        activeColor: kblue,
      ),
    );
  }
  
  Widget _buildLogLevelDropdown() {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: const Text('Log Level'),
        subtitle: const Text('Minimum level of logs to show'),
        trailing: DropdownButton<LogLevel>(
          value: _config.logLevel,
          onChanged: (LogLevel? newValue) {
            if (newValue != null) {
              setState(() {
                _config.setLogLevel(newValue);
              });
            }
          },
          items: LogLevel.values.map<DropdownMenuItem<LogLevel>>((LogLevel value) {
            return DropdownMenuItem<LogLevel>(
              value: value,
              child: Text(value.name.toUpperCase()),
            );
          }).toList(),
        ),
      ),
    );
  }
  
  Widget _buildPresetButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Quick Presets'),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildPresetButton('Disabled', LoggingPreset.disabled),
            _buildPresetButton('Minimal', LoggingPreset.minimal),
            _buildPresetButton('Development', LoggingPreset.development),
            _buildPresetButton('Production', LoggingPreset.production),
            _buildPresetButton('Debugging', LoggingPreset.debugging),
          ],
        ),
      ],
    );
  }
  
  Widget _buildPresetButton(String label, LoggingPreset preset) {
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _config.applyPreset(preset);
        });
        Get.snackbar(
          'Preset Applied',
          '$label preset has been applied',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kblue,
          colorText: kwhite,
        );
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: kblue,
        foregroundColor: kwhite,
      ),
      child: Text(label),
    );
  }
  
  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton(
          onPressed: () {
            setState(() {
              _config.resetToDefaults();
            });
            Get.snackbar(
              'Reset Complete',
              'Logging configuration has been reset to defaults',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: kblue,
              colorText: kwhite,
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: kwhite,
          ),
          child: const Text('Reset to Defaults'),
        ),
        const SizedBox(height: 8),
        ElevatedButton(
          onPressed: () {
            _showConfigInfo();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: kblue,
            foregroundColor: kwhite,
          ),
          child: const Text('View Current Configuration'),
        ),
        const SizedBox(height: 8),
        ElevatedButton(
          onPressed: () {
            _testLogging();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: kwhite,
          ),
          child: const Text('Test Logging'),
        ),
      ],
    );
  }
  
  void _showConfigInfo() {
    final config = _config.getAllConfig();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Current Configuration'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: config.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Text('${entry.key}: ${entry.value}'),
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
  
  void _testLogging() {
    final logger = LoggingService.instance;
    
    // Test different log levels
    logger.log(LogLevel.debug, '🧪 Test debug message');
    logger.log(LogLevel.info, '🧪 Test info message');
    logger.log(LogLevel.warning, '🧪 Test warning message');
    logger.log(LogLevel.error, '🧪 Test error message');
    
    // Test API logging
    logger.logApiRequest(
      method: 'GET',
      url: 'https://api.example.com/test',
      headers: {'Content-Type': 'application/json'},
      body: {'test': 'data'},
    );
    
    // Test navigation logging
    logger.logNavigation(
      action: 'test',
      from: 'LoggingConfigScreen',
      to: 'TestScreen',
      arguments: {'test': true},
    );
    
    // Test interaction logging
    logger.logInteraction(
      type: 'tap',
      element: 'test_button',
      screen: 'LoggingConfigScreen',
      data: {'test': true},
    );
    
    Get.snackbar(
      'Test Complete',
      'Check your debug console for test log messages',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: kwhite,
    );
  }
}
