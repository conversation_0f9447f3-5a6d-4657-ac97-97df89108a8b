import 'package:flutter/material.dart';

class GridCard extends StatelessWidget {
  const GridCard({Key? key, required this.index}) : super(key: key);
  final int index;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 165,
      width: 171,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Image.asset(cards[index]),
    );
  }
}

List cards = [
  'assets/images/offerimages/pulses.png',
  'assets/images/offerimages/vegetables.png',
  'assets/images/offerimages/fruits.png',
  'assets/images/offerimages/milkproducts.png'
];
