import 'package:flutter/material.dart';

class HorizontalOfferCard extends StatelessWidget {
  const HorizontalOfferCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Container(
        width: 334,
        height: 184,
        decoration: BoxDecoration(
            color: Colors.blue, borderRadius: BorderRadius.circular(16)),
      ),
    );
  }
}
