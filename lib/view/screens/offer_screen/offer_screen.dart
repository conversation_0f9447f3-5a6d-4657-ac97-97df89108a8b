// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:carousel_slider/carousel_slider.dart';
// import 'package:page_view_dot_indicator/page_view_dot_indicator.dart';
// import 'package:rapsap/view/widgets/commons.dart';
// import 'package:rapsap/controllers/offerscrrencontroller.dart';
// import 'package:rapsap/view/screens/offer_screen/widget/shop_by_offer_card.dart';

// import '../../../controllers/home_view_controller.dart';
// import '../mapscreen/searchpage.dart';
// import '../product_screen/product_screen.dart';
// import 'widget/grid_card.dart';

// class OfferScreen extends StatelessWidget {
//   OfferScreen({Key? key}) : super(key: key);
//   final HomeViewController controller = Get.put(HomeViewController());

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         titleSpacing: 24,
//         backgroundColor: Get.theme.scaffoldBackgroundColor,
//         elevation: 0,
//         scrolledUnderElevation: 3,
//         automaticallyImplyLeading: false,
//         // leading: IconButton(
//         //   icon: const Icon(Icons.arrow_back),
//         //   onPressed: () {},
//         // ),
//         foregroundColor: kblack,
//         title: const Text(
//           'Offers',
//           style: TextStyle(color: kblack, fontWeight: FontWeight.w600),
//         ),
//         actions: [
//           SvgPicture.asset(
//             'assets/svg/search-icon.svg',
//             height: 16,
//           ),
//           kwidth20,
//           SvgPicture.asset(
//             'assets/svg/settings.svg',
//             height: 16,
//           ),
//           kwidth30,
//         ],
//         bottom: const PreferredSize(
//           preferredSize: Size.fromHeight(15),
//           child: CommonDivider(),
//         ),
//       ),
//       body: SingleChildScrollView(
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
//               child: Stack(
//                 children: [
//                   CarouselSlider.builder(
//                     itemCount: controller.bannerItems.length,
//                     itemBuilder: (BuildContext context, int itemIndex,
//                         int pageViewIndex) {
//                       if (controller.bannerItems.isEmpty) {
//                         return SizedBox();
//                       }
//                       return controller.bannerItems[itemIndex].bannerImage ==
//                               null
//                           ? controller.bannerimage
//                           : InkWell(
//                               onTap: () {
//                                 if (controller
//                                             .bannerItems[itemIndex].productId !=
//                                         null &&
//                                     controller.bannerItems[itemIndex].type ==
//                                         "product") {
//                                   // Get.to(() => ProductScreen(
//                                   //       productId: controller
//                                   //           .bannerItems[itemIndex].productId!,
//                                   //     ));
//                                 }
//                               },
//                               child: CachedNetworkImage(
//                                 fit: BoxFit.fitHeight,
//                                 imageUrl: controller
//                                     .bannerItems[itemIndex].bannerImage!,
//                                 placeholder: (context, url) =>
//                                     const Center(child: SizedBox()),
//                                 errorWidget: (context, url, error) =>
//                                     controller.bannerimage,
//                               ),
//                             );
//                     },
//                     options: CarouselOptions(
//                       onPageChanged: ((index, reason) {
//                         controller.selectedpageitem = index;
//                         controller.update();
//                       }),
//                       initialPage: 0,
//                       aspectRatio: 2.0,
//                       height: 180,
//                       viewportFraction: 1,
//                       padEnds: true,
//                       clipBehavior: Clip.antiAlias,
//                       autoPlay: true,
//                     ),
//                   ),
//                   controller.bannerItems.length > 1
//                       ? Positioned(
//                           left: -25,
//                           bottom: 20,
//                           child: GetBuilder<HomeViewController>(
//                             builder: (controller) {
//                               return PageViewDotIndicator(
//                                 unselectedSize: const Size(8, 6),
//                                 size: const Size(10, 8),
//                                 currentItem: controller.selectedpageitem,
//                                 count: controller.bannerItems.length,
//                                 unselectedColor: const Color(0xff556f80),
//                                 selectedColor: Colors.white,
//                               );
//                             },
//                           ),
//                         )
//                       : SizedBox()
//                 ],
//               ),
//             ),

//             //offer grid view
//             GridView.builder(
//               semanticChildCount: 2,
//               physics: const NeverScrollableScrollPhysics(),
//               gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                 crossAxisCount: 2,
//                 crossAxisSpacing: 20,
//                 mainAxisSpacing: 20,
//               ),
//               shrinkWrap: true,
//               padding: const EdgeInsets.all(15),
//               itemCount: 4,
//               itemBuilder: (BuildContext context, int index) {
//                 return GridCard(
//                   index: index,
//                 );
//               },
//             ).paddingSymmetric(horizontal: 8),
//             const SizedBox(
//               height: 50,
//             ),

//             //Horizontal scroller offers
//             Container(
//               height: 300,
//               color: Color(0xffEBF3E9),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   kheight20,
//                   const Padding(
//                       padding: EdgeInsets.only(left: 24),
//                       child: Text(
//                         'Trending Deals',
//                         style: TextStyle(
//                             fontSize: 24, fontWeight: FontWeight.bold),
//                       )),
//                   const SizedBox(
//                     height: 7,
//                   ),
//                   SizedBox(
//                     height: 200,
//                     child: ListView.builder(
//                       padding: EdgeInsets.zero,
//                       scrollDirection: Axis.horizontal,
//                       itemBuilder: (context, index) => Container(
//                         child: ClipRRect(
//                           child: Image.asset(
//                             "assets/images/banner-2.png",
//                           ),
//                         ),
//                       ),
//                       itemCount: 5,
//                     ),
//                   ).paddingSymmetric(horizontal: 24),
//                 ],
//               ),
//             ),
//             const SizedBox(
//               height: 50,
//             ),
//             const Padding(
//               padding: EdgeInsets.only(left: 24),
//               child: Text('Shop By Offers',
//                   style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
//             ),
//             //shop by offers list
//             ListView(
//               shrinkWrap: true,
//               physics: const NeverScrollableScrollPhysics(),
//               children: const [
//                 ShopByOfferCard(
//                   imageurl: 'assets/images/offerimages/shopbyoffers1.png',
//                 ),
//                 ShopByOfferCard(
//                   imageurl: 'assets/images/offerimages/shopbyoffers2.png',
//                 ),
//                 ShopByOfferCard(
//                   imageurl: 'assets/images/offerimages/shopbyoffers3.png',
//                 ),
//                 kheight50,
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
