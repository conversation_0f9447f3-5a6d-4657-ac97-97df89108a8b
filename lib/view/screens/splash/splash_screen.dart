import 'dart:developer';

import 'package:geocoding/geocoding.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:rapsap/main.dart';
import 'package:rapsap/model/user_model/usermodel.dart';
import 'package:rapsap/view/screens/mapscreen/mappage.dart';
import 'package:rapsap/view/screens/root_page/root_page.dart';

import '../../../controllers/accountscontroller.dart';
import '../../../controllers/wishlistcontroller.dart';
import '../../../services/firebaseservices.dart';
import '../../../services/userservices.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final UserController _userController = Get.find<UserController>();
  
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Wait a moment for Firebase to be fully ready if it's still initializing
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Firebase analytics calls (with safety checks)
      await _logAppOpen();
      
      FocusManager.instance.primaryFocus?.unfocus();

      if (storage.read('loginstatus') != null &&
          storage.read('userdata') != null) {
        _userController.loginstatus.value = storage.read('loginstatus');

        _userController.userdata.value =
            UserData.fromJson(storage.read('userdata'));
            
        await getUserConfig();
        await _setUserIdAnalytics();
        
        // Initialize controllers
        Get.isRegistered<AccountController>()
            ? Get.find<AccountController>()
            : Get.put(AccountController());
            
        final Wishlistcontroller wishlistcontroller = Get.find();
        wishlistcontroller.getwishlistitems();
      }

      if (storage.read('locationaddress') != null) {
        _userController.locationaddress.value =
            Placemark.fromMap(storage.read('locationaddress'));
      }

      debugPrint(_userController.userdata.value.data.toString());
      
      // Navigate after initialization
      _navigateToNextScreen();
      
    } catch (e) {
      log('Error in splash screen initialization: $e');
      // Still navigate even if there's an error
      _navigateToNextScreen();
    }
  }

  void _navigateToNextScreen() {
    if (!mounted) return;
    
    if (_userController.userdata.value.data?.deleted == 1) {
      Future.delayed(
          const Duration(milliseconds: 1000),
          () {
            if (mounted) {
              Get.to(() => const RootPage(data: true));
            }
          });
    } else {
      Future.delayed(
          const Duration(milliseconds: 2000),
          () {
            if (mounted) {
              Get.to(
                  () => _userController.loginstatus.isFalse
                      ? const LoginScreen()
                      : storage.read('tempPincode') == null
                          ? MapPage()
                          : const RootPage(),
                  transition: Transition.fadeIn);
            }
          });
    }
  }

  Future<void> _setUserIdAnalytics() async {
    try {
      if (FirebaseService.isInitialized && 
          _userController.userdata.value.data?.id != null) {
        await FirebaseService.setUserId(
            _userController.userdata.value.data!.id.toString());
      }
    } catch (e) {
      log('Error setting user ID for analytics: $e');
    }
  }

  Future<void> _logAppOpen() async {
    try {
      await FirebaseService.logAppOpen();
    } catch (e) {
      log('Error logging app open: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
            height: double.infinity,
            width: double.infinity,
            decoration: const BoxDecoration(
              color: Colors.black,
              image: DecorationImage(image: AssetImage(splashbackground)),
            ),
            child: Center(child: SvgPicture.asset(logo))));
  }

  Future<void> getUserConfig() async {
    try {
      final data = await UserService.getUserConfig("splash");
      debugPrint("getUserConfig result: $data");
      if (data == true) {
        if (mounted) {
          Get.to(const RootPage(data: true));
        }
        return;
      }
    } catch (e) {
      log('Error getting user config: $e');
    }
  }
}