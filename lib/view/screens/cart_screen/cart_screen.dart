import 'dart:developer';
import 'dart:io';

import 'package:rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:rapsap/view/screens/mapscreen/setlocation.dart';
import 'package:rapsap/view/screens/ordersection/orderprocessing.dart';
import 'package:rapsap/view/widgets/animationviewcart.dart';
import 'package:flutter/services.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:rapsap/view/screens/cart_screen/applycoupon.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:rapsap/controllers/accountscontroller.dart';
import 'package:rapsap/controllers/cartcontrolller.dart';
import 'package:rapsap/controllers/product_view_controller.dart';
import 'package:rapsap/controllers/root_view_controller.dart';
import 'package:rapsap/services/orderservices.dart';
import 'package:rapsap/view/screens/AddressPage/selectaddress.dart';
import 'package:rapsap/view/screens/login/widgets/button.dart';
import 'package:rapsap/view/screens/root_page/root_page.dart';
import 'package:rapsap/view/widgets/custom.dart';
import 'package:shimmer/shimmer.dart';
// ignore: depend_on_referenced_packages
import 'package:fluttertoast/fluttertoast.dart';

import '../../../main.dart';

import '../../../model/configmodel.dart';
import '../../../services/databaseHelper.dart';
import '../ordersection/paymentmethod.dart';
import '../product_screen/product_screen.dart';

List carlistModel = [];
ConfigModel configModel =
    const ConfigModel(deliveryFeeThreshold: '0', deliveryFee: '0');

class CartScreen extends StatefulWidget {
  const CartScreen({Key? key, this.type}) : super(key: key);
  final String? type;

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  // late Orders orders;
  String couponText = '';
  final couponTextCTRL = TextEditingController();
  int update = 0;
  bool isLoading = true;
  final AccountController accountController =
      Get.isRegistered<AccountController>()
          ? Get.find<AccountController>()
          : Get.put(AccountController());

  @override
  void initState() {
    super.initState();
    // setupOrder();

    getProducts();
    accountController.getaddress();

    getConfig();
  }

  Future getProducts() async {
    print("get Products called");
    List<ShopingCart> list = await DatabaseHelper.instance.getGroceries();
    controller.myCartItems.value = list;
    controller.loading.value = false;
    controller.update();
    if (mounted) {
      setState(() {
        controller.myCartItems.value = list;
      });
    }
  }

  getConfig() async {
    // SharedPreferences share = await SharedPreferences.getInstance();
    // String userTempID = (share.getString('userTempID') ?? '');
    // if (userTempID.isEmpty) {
    var res = await OrderServices.getConfig();
    print('res config $res');
    if (mounted) {
      setState(() {
        configModel = res!;
      });
    }
    // } else {
    //   // Temp user is logged in...
    // }
  }

  final CartController controller = Get.find<CartController>();

  final RootViewController rootViewController = Get.find<RootViewController>();

  final ProductViewController productViewController =
      Get.find<ProductViewController>();

  final UserController userController = Get.find<UserController>();

  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    controller.buttonloading = false;

    return WillPopScope(
      onWillPop: (() async {
        Get.offAll(() => const RootPage(), transition: Transition.leftToRight);
        return false;
      }),
      child: GestureDetector(
        onHorizontalDragUpdate: Platform.isIOS
            ? (details) {
                //set the sensitivity for your ios gesture anywhere between 10-50 is good

                int sensitivity = 8;

                if (details.delta.dx > sensitivity) {
                  Get.offAll(() => const RootPage(),
                      transition: Transition.leftToRight);

                  //SWIPE FROM RIGHT DETECTION
                }
              }
            : null,
        child: Scaffold(
            appBar: AppBar(
                backgroundColor: kwhite,
                toolbarHeight: 0,
                elevation: 0,
                bottom: PreferredSize(
                  preferredSize: const Size.fromHeight(65),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Padding(
                                  padding: const EdgeInsets.only(
                                      left: 20, right: 20),
                                  child: widget.type != null
                                      ? const SizedBox()
                                      : IconButton(
                                          onPressed: () {
                                            Get.back();
                                          },
                                          icon: SvgPicture.asset(
                                              'assets/svg/iconback.svg'))),
                              Obx(() => Text(
                                    controller.myCartItems.value.isEmpty
                                        ? 'My Cart'
                                        : 'My Cart (${controller.myCartItems.fold<int>(0, (previousValue, element) => previousValue + element.qty!)})',
                                    style: const TextStyle(
                                        fontSize: 20,
                                        color: kblack,
                                        fontWeight: FontWeight.w700),
                                  )),
                            ],
                          ),
                        ],
                      ),
                      widget.type != null ? kheight20 : kheight10,
                    ],
                  ),
                )),
            body: Stack(
              children: [
                Column(
                  children: [
                    Obx(() => controller.myCartItems.isEmpty
                        ? const SizedBox()
                        : StreamBuilder<List<Orders>>(
                            stream: DatabaseHelper.instance
                                .getDbOrder1()
                                .asStream(),
                            builder: (context, snapshot) {
                              return snapshot.data != null
                                  ? snapshot.data!.first.discount == 0.0 ||
                                          snapshot.data!.first.discount == null
                                      ? const SizedBox()
                                      : Column(
                                          children: [
                                            Container(
                                              color: const Color(0xffE1E8FF),
                                              width: double.infinity,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal:
                                                          defaultpadding,
                                                      vertical: 10),
                                              child: Text(
                                                'You will save ₹ ${snapshot.data!.first.discount ?? ""} on this order',
                                                style: GoogleFonts.dmSans(
                                                    color: kblue,
                                                    fontSize: 17,
                                                    letterSpacing: -0.4,
                                                    fontWeight:
                                                        FontWeight.w700),
                                              ),
                                            ),
                                          ],
                                        )
                                  : const SizedBox();
                            })),
                    Expanded(
                      child: CustomScrollView(
                        controller: _scrollController,
                        physics: const BouncingScrollPhysics(),
                        slivers: [
                          SliverToBoxAdapter(
                              child: Obx(
                            () => controller.myCartItems.isEmpty
                                ? SizedBox(
                                    height: Get.height * 0.5,
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SvgPicture.asset(
                                            'assets/svg/empty_cart.svg'),
                                        kheight20,
                                        const Text(
                                          'Your cart is empty!',
                                          style: TextStyle(
                                              fontSize: 24,
                                              fontWeight: FontWeight.w600),
                                        ),
                                        kheight30,
                                        SizedBox(
                                            width: Get.width * 0.4,
                                            height: 45,
                                            child: SubmitButton(
                                                text: 'Shop Now',
                                                onpress: () {
                                                  Get.to(
                                                      () => const RootPage());
                                                }))
                                      ],
                                    ),
                                  )
                                : const SizedBox(),
                          )),
                          SliverToBoxAdapter(
                            child: storage.read('loginstatus') == true
                                ? Obx(() => controller.myCartItems.isEmpty
                                    ? const SizedBox()
                                    : StreamBuilder<List<Orders>>(
                                        stream: DatabaseHelper.instance
                                            .getDbOrder1()
                                            .asStream(),
                                        builder: (context, snapshot) {
                                          if (snapshot.hasData) {
                                            if (snapshot.data!.first
                                                        .couponCode !=
                                                    null &&
                                                snapshot.data!.first
                                                        .couponCode !=
                                                    "") {
                                              return CommonListTile(
                                                  text: snapshot
                                                      .data!.first.couponCode!,
                                                  leadingicon: Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            right: 8.0, top: 5),
                                                    child: SvgPicture.asset(
                                                      'assets/svg/promocode.svg',
                                                      width: 14,
                                                    ),
                                                  ),
                                                  onpress: () {
                                                    Get.to(() => CouponPage());
                                                  });
                                            } else {
                                              return CommonListTile(
                                                  text: 'Apply Coupon',
                                                  leadingicon: Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            right: 8.0, top: 5),
                                                    child: SvgPicture.asset(
                                                      'assets/svg/promocode.svg',
                                                      width: 14,
                                                    ),
                                                  ),
                                                  onpress: () {
                                                    Get.to(() => CouponPage());
                                                  });
                                            }
                                          } else {
                                            return CommonListTile(
                                                text: 'Apply Coupon',
                                                leadingicon: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          right: 8.0, top: 5),
                                                  child: SvgPicture.asset(
                                                    'assets/svg/promocode.svg',
                                                    width: 14,
                                                  ),
                                                ),
                                                onpress: () {
                                                  Get.to(() => CouponPage());
                                                });
                                          }
                                        }))
                                : const SizedBox(),
                          ),
                          const SliverPadding(
                            padding: EdgeInsets.symmetric(vertical: 5),
                          ),
                          SliverToBoxAdapter(
                              // if (index == 2) {
                              //   return const SubscribtionCartCard();
                              // }
                              child: Obx(() => Column(
                                    children: [
                                      SizedBox(
                                          child: controller.loading.value
                                              ? Center(
                                                  child: _CartCardShimmer()
                                                      .paddingSymmetric(
                                                          horizontal: 16,
                                                          vertical: 16))
                                              : controller
                                                      .myCartItems.isNotEmpty
                                                  ? ListView.builder(
                                                      physics:
                                                          const NeverScrollableScrollPhysics(),
                                                      shrinkWrap: true,
                                                      itemCount: controller
                                                          .myCartItems.length,
                                                      itemBuilder:
                                                          (context, index) {
                                                        return cartItemCard(
                                                            index);
                                                      })
                                                  : const SizedBox()),
                                    ],
                                  ))),
                          const SliverPadding(
                            padding: EdgeInsets.all(5),
                          ),
                          SliverToBoxAdapter(
                              child: Obx(() => controller.myCartItems.isEmpty
                                  ? const SizedBox()
                                  : Container(
                                      color: kwhite,
                                      child: Column(
                                        children: [
                                          kheight20,
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: defaultpadding),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                const Text(
                                                  'Price Details',
                                                  style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.w700,
                                                      fontSize: 18),
                                                ),
                                                kheight20,
                                                FutureBuilder<double>(
                                                    future: DatabaseHelper
                                                        .instance
                                                        .getSubTotal(),
                                                    builder:
                                                        (context, snapshot) {
                                                      return Obx(
                                                          () => PriceRowsWidget(
                                                                amount:
                                                                    '₹${snapshot.data ?? ""}',
                                                                title:
                                                                    'Price ( ${controller.myCartItems.length} ) Items',
                                                              ));
                                                    }),
                                                hgap(16),
                                                StreamBuilder<List<Orders>>(
                                                    stream: DatabaseHelper
                                                        .instance
                                                        .getDbOrder1()
                                                        .asStream(),
                                                    builder:
                                                        (context, snapshot) {
                                                      if (snapshot.hasData) {
                                                        print(snapshot.data);
                                                        if (snapshot.data !=
                                                                null &&
                                                            snapshot.data!
                                                                .isNotEmpty) {
                                                          Orders order =
                                                              snapshot.data![0];
                                                          return Column(
                                                            children: [
                                                              PriceRowsWidget(
                                                                amount: order
                                                                            .discount
                                                                            .toString() ==
                                                                        "null"
                                                                    ? "0"
                                                                    : order
                                                                        .discount
                                                                        .toString(),
                                                                title:
                                                                    'Discount',
                                                              ),
                                                              hgap(16),
                                                              PriceRowsWidget(
                                                                amount: getGrandTotal(
                                                                            order) >
                                                                        double.parse(configModel
                                                                            .deliveryFeeThreshold
                                                                            .toString())
                                                                    ? 'Free'
                                                                    : "₹ ${double.parse(configModel.deliveryFee.toString())}",
                                                                title:
                                                                    'Delivery fee',
                                                              ),
                                                              hgap(16),
                                                              PriceRowsWidget(
                                                                amount: getTaxValue(
                                                                        order)
                                                                    .toString(),
                                                                title: 'Taxes',
                                                              ),
                                                              kheight20,
                                                              SizedBox(
                                                                width: double
                                                                    .infinity,
                                                                height: 1,
                                                                child:
                                                                    Container(
                                                                  decoration:
                                                                      BoxDecoration(
                                                                          gradient: LinearGradient(
                                                                              begin: Alignment.topLeft,
                                                                              colors: [
                                                                        kblue.withOpacity(
                                                                            0.05),
                                                                        kblue.withOpacity(
                                                                            0.1),
                                                                        kblue.withOpacity(
                                                                            0.2),
                                                                        kblue.withOpacity(
                                                                            0.3),
                                                                        kblue.withOpacity(
                                                                            0.4),
                                                                        kblue.withOpacity(
                                                                            0.3),
                                                                        kblue.withOpacity(
                                                                            0.2),
                                                                        kblue.withOpacity(
                                                                            0.1),
                                                                        kblue.withOpacity(
                                                                            0.05),
                                                                      ])),
                                                                ),
                                                              ),
                                                            ],
                                                          );
                                                        } else {
                                                          return const SizedBox();
                                                        }
                                                      } else {
                                                        return const SizedBox();
                                                      }
                                                    }),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    const Text(
                                                      'Total Amount',
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w700,
                                                      ),
                                                    ),
                                                    StreamBuilder<List<Orders>>(
                                                      stream: DatabaseHelper
                                                          .instance
                                                          .getDbOrder1()
                                                          .asStream(),
                                                      builder:
                                                          (context, snapshot) {
                                                        if (snapshot.data !=
                                                            null) {
                                                          return Text(
                                                            '₹${snapshot.data!.isNotEmpty ? getGrandTotal(snapshot.data!.first).toString() : 0.0}',
                                                            style: const TextStyle(
                                                                fontSize: 16,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w700),
                                                          );
                                                        } else {
                                                          return const Padding(
                                                            padding:
                                                                EdgeInsets.all(
                                                                    2.0),
                                                            child: Center(
                                                                child: CustomAppShimmer(
                                                                    child: Text(
                                                                        '...'))),
                                                          );
                                                        }
                                                      },
                                                    )
                                                  ],
                                                ).paddingSymmetric(
                                                    vertical: 20),
                                              ],
                                            ),
                                          ),
                                          hgap(180)
                                        ],
                                      ),
                                    ))),
                        ],
                      ),
                    ),
                  ],
                ),
                GetBuilder<CartController>(
                  id: 'total',
                  builder: (controller) {
                    if (controller.myCartItems.isEmpty) {
                      return const SizedBox();
                    } else {
                      return Obx(() {
                        return Align(
                          alignment: Alignment.bottomCenter,
                          child: userController.userdata.value.data == null
                              ? AnimatedCartCard(
                                  // delay: 200,
                                  height: 80,
                                  // delay: 200,
                                  child: SubmitButton(
                                          textsize: 16,
                                          bgcolor: kblue,
                                          text: 'Login to Continue',
                                          onpress: () {
                                            Get.to(() => const LoginScreen(),
                                                fullscreenDialog: true);
                                          })
                                      .paddingSymmetric(
                                          horizontal: defaultpadding,
                                          vertical: 8)
                                      .paddingOnly(bottom: 15))
                              : accountController.addressModel.value.data ==
                                      null
                                  ? const SizedBox()
                                  : accountController
                                          .addressModel.value.data!.isEmpty
                                      ? AnimatedCartCard(
                                          height: 80,
                                          child: SubmitButton(
                                                  textsize: 16,
                                                  bgcolor: kblue,
                                                  text: 'Enter delivery address',
                                                  onpress: () {
                                                    Get.to(() =>
                                                        SelectAddressScreen());
                                                    Get.to(() =>
                                                        AddressAddEditPage(
                                                            mode: addEditAddress
                                                                .add));
                                                  })
                                              .paddingSymmetric(
                                                  horizontal: defaultpadding,
                                                  vertical: 8)
                                              .paddingOnly(bottom: 15))
                                      : AnimatedCartCard(
                                          // decoration:
                                          //     BoxDecoration(color: kwhite, boxShadow: [
                                          //   BoxShadow(
                                          //       color: kblack.withOpacity(0.5),
                                          //       blurRadius: 6,
                                          //       spreadRadius: 0,
                                          //       offset: const Offset(0.0, 3.0))
                                          // ]),

                                          height: Platform.isIOS ? 160 : 150,
                                          // decoration:
                                          //     BoxDecoration(color: kwhite, boxShadow: [
                                          //   BoxShadow(
                                          //       color: kblack.withOpacity(0.5),
                                          //       blurRadius: 6,
                                          //       spreadRadius: 0,
                                          //       offset: const Offset(0.0, 3.0))
                                          // ]),

                                          child: Container(
                                            color: kwhite,
                                            child: Column(
                                              children: [
                                                controller.myCartItems
                                                            .isEmpty ||
                                                        accountController
                                                                .selectedAddress
                                                                .value
                                                                .pincode ==
                                                            null
                                                    ? const SizedBox()
                                                    : Flexible(
                                                        flex: 2,
                                                        child: Container(
                                                          color: Colors.white60,
                                                          child: Column(
                                                            children: [
                                                              Flexible(
                                                                child: ListTile(
                                                                  tileColor: Colors
                                                                      .white60,
                                                                  horizontalTitleGap:
                                                                      0,
                                                                  minLeadingWidth:
                                                                      0,
                                                                  title:
                                                                      Padding(
                                                                    padding: const EdgeInsets
                                                                            .only(
                                                                        left: 8,
                                                                        right:
                                                                            2),
                                                                    child: Text(
                                                                      'Deliver to ${accountController.selectedAddress.value.address1}, ${accountController.selectedAddress.value.address2}, ${accountController.selectedAddress.value.landmark}, ${accountController.selectedAddress.value.city}, ${accountController.selectedAddress.value.state}, ${accountController.selectedAddress.value.pincode}, ${accountController.selectedAddress.value.phone}',
                                                                      style:
                                                                          const TextStyle(),
                                                                      overflow:
                                                                          TextOverflow
                                                                              .ellipsis,
                                                                    ),
                                                                  ),
                                                                  leading:
                                                                      Padding(
                                                                    padding: const EdgeInsets
                                                                            .only(
                                                                        left:
                                                                            0),
                                                                    child: SvgPicture
                                                                        .asset(
                                                                      'assets/svg/location_cart.svg',
                                                                      color:
                                                                          kblack,
                                                                    ),
                                                                  ),
                                                                  trailing:
                                                                      InkWell(
                                                                    onTap: () {
                                                                      Get.to(() =>
                                                                          SelectAddressScreen());
                                                                    },
                                                                    child:
                                                                        Container(
                                                                      decoration:
                                                                          const BoxDecoration(
                                                                        border:
                                                                            Border(
                                                                          bottom:
                                                                              BorderSide(color: kblue),
                                                                        ),
                                                                      ),
                                                                      child:
                                                                          const Text(
                                                                        'Change',
                                                                        style: TextStyle(
                                                                            color:
                                                                                kblue),
                                                                      ).paddingAll(
                                                                        2,
                                                                      ),
                                                                    ),
                                                                  ).paddingOnly(
                                                                          left:
                                                                              20),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                Flexible(
                                                  flex: 3,
                                                  child: Container(
                                                    decoration:
                                                        const BoxDecoration(
                                                      color: kblue,
                                                      //     boxShadow: <
                                                      //         BoxShadow>[
                                                      //   BoxShadow(
                                                      //       color: kgrey,
                                                      //       blurRadius: 8.0,
                                                      //       offset: Offset(
                                                      //           0.0, 0.75))
                                                      // ]
                                                    ),
                                                    child: Padding(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                        horizontal:
                                                            defaultpadding,
                                                      ),
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Flexible(
                                                            child: Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .center,
                                                              children: [
                                                                const Flexible(
                                                                  child: Text(
                                                                    'Total Amount',
                                                                    style:
                                                                        TextStyle(
                                                                      color:
                                                                          kwhite,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w400,
                                                                    ),
                                                                  ),
                                                                ),
                                                                StreamBuilder<
                                                                    List<
                                                                        Orders>>(
                                                                  stream: DatabaseHelper
                                                                      .instance
                                                                      .getDbOrder1()
                                                                      .asStream(),
                                                                  builder: (context,
                                                                      snapshot) {
                                                                    if (snapshot
                                                                            .data !=
                                                                        null) {
                                                                      print(
                                                                          'getgranttotal');
                                                                      return Flexible(
                                                                        child:
                                                                            Text(
                                                                          '₹${snapshot.data!.isNotEmpty ? getGrandTotal(snapshot.data!.first).toString() : 0.0}',
                                                                          style: const TextStyle(
                                                                              color: kwhite,
                                                                              fontSize: 18,
                                                                              fontWeight: FontWeight.w700),
                                                                        ),
                                                                      );
                                                                    } else {
                                                                      return const Flexible(
                                                                        child:
                                                                            Padding(
                                                                          padding:
                                                                              EdgeInsets.all(2.0),
                                                                          child:
                                                                              Center(child: Text('')),
                                                                        ),
                                                                      );
                                                                    }
                                                                  },
                                                                )
                                                              ],
                                                            ),
                                                          ),
                                                          GetBuilder<
                                                                  CartController>(
                                                              builder: (_) {
                                                            return controller
                                                                    .buttonloading
                                                                ? const SizedBox(
                                                                    height: 20,
                                                                    child:
                                                                        LoadingIndicator(
                                                                      colors: [
                                                                        kblue,
                                                                        kblack
                                                                      ],
                                                                      indicatorType:
                                                                          Indicator
                                                                              .cubeTransition,
                                                                    ),
                                                                  ).paddingOnly(
                                                                    right: 20)
                                                                : MaterialButton(
                                                                    padding: const EdgeInsets
                                                                            .symmetric(
                                                                        horizontal:
                                                                            40),
                                                                    textColor:
                                                                        kwhite,
                                                                    color:
                                                                        kblack,
                                                                    onPressed:
                                                                        () async {
                                                                      HapticFeedback
                                                                          .vibrate();
                                                                      controller
                                                                              .buttonloading =
                                                                          true;
                                                                      controller
                                                                          .update();
                                                                      await _scrollController
                                                                          .animateTo(
                                                                        _scrollController
                                                                            .position
                                                                            .maxScrollExtent,
                                                                        curve: Curves
                                                                            .easeOut,
                                                                        duration:
                                                                            const Duration(milliseconds: 500),
                                                                      );
                                                                      createOrderNavigate();
                                                                    },
                                                                    child:
                                                                        const Text(
                                                                      'Pay now',
                                                                      style: TextStyle(
                                                                          fontSize:
                                                                              16,
                                                                          fontWeight:
                                                                              FontWeight.w500),
                                                                    ),
                                                                  );
                                                          })
                                                        ],
                                                      ),
                                                    ).paddingOnly(
                                                        top: Platform.isIOS
                                                            ? 25
                                                            : 15,
                                                        bottom: Platform.isIOS
                                                            ? 35
                                                            : 20),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          // width: double.infinity,
                                        ),
                        );
                      });
                    }
                  },
                ),
              ],
            )),
      ),
    );
  }

  void createOrderNavigate() async {
    
    controller.buttonloading = true;
    controller.update();
    List<Orders> odrList = await DatabaseHelper.instance.getDbOrder();
    Orders order = odrList[0];
    if (getGrandTotal(order) <
        double.parse(configModel.minOrderValue.toString())) {
      // EasyLoading.dismiss();
      controller.buttonloading = false;
      controller.update();
      customToast(
          backgroundColor: kblue,
          gravity: ToastGravity.TOP,
          message:
              'Min. order amount should be greater than ₹${configModel.minOrderValue.toString()}');

      return;
    }
    List<ShopingCart> itemlist = await DatabaseHelper.instance.getGroceries();

    if (odrList.isNotEmpty && itemlist.isNotEmpty) {
      controller.buttonloading = false;
      controller.update();

 return Get.to(() => OrderProcessing(
  orderType: "COD",
            itemlist: itemlist,
            order: order,
          ));
      // return Get.to(() => OrderProcessing(
      //       itemlist: itemlist,
      //       order: order,
      //     ));
    }
  }

  double getGrandTotal(Orders order) {
    print('grandtotal');
    double? discount = order.discount ?? 0.0;
    log(discount.toString());
    print(order.subTotal);
    double grandtotal = (order.subTotal! - discount);
    // return grandtotal.floorToDouble();
    print(grandtotal);
    if (grandtotal >
        double.parse(configModel.deliveryFeeThreshold.toString())) {
      return grandtotal.floorToDouble();
    }
    return (grandtotal + double.parse(configModel.deliveryFee.toString()))
        .floorToDouble();
  }

  double getTaxValue(Orders order) {
    double? discount = order.discount ?? 0.0;
    double? tax = order.tax ?? 0.0;
    double taxValue = ((order.subTotal! - discount) * tax) / 100;
    return taxValue.floorToDouble();
  }

  Container cartItemCard(index) {
    var discountVal = controller.myCartItems[index].mrp != null
        ? ((100 *
                    (double.parse(
                            controller.myCartItems[index].mrp.toString()) -
                        double.parse(
                            controller.myCartItems[index].price.toString()))) /
                double.parse(controller.myCartItems[index].mrp.toString()))
            .floorToDouble()
        : 0;

    return Container(
      padding:
          const EdgeInsets.symmetric(horizontal: defaultpadding, vertical: 6),
      color: kwhite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          index == 0
              ? Text(
                  'Review Products',
                  style: GoogleFonts.dmSans(
                      fontWeight: FontWeight.w700, fontSize: 20),
                ).paddingSymmetric(vertical: 20)
              : const SizedBox(),
          Container(
            color: kwhite,
            child: GestureDetector(
              onTap: controller.myCartItems[index].isFree == 1
                  ? null
                  : (() async {
                      await showProductScreenSheet(
                          productId: controller.myCartItems[index].productID,
                          variantId: controller.myCartItems[index].variantID);
                      // Get.to(() => ProductScreen(
                      //   variantId: controller.myCartItems[index].variantID,
                      //   productId: controller.myCartItems[index].productID));
                    }),
              child: Column(
                children: [
                  Stack(
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Expanded(
                                flex: 2,
                                child: controller.myCartItems[index].imageURL ==
                                            null ||
                                        controller
                                                .myCartItems[index].imageURL ==
                                            ""
                                    ? Image.asset(
                                        "assets/images/error-image.png")
                                    : Image.network(controller
                                        .myCartItems[index].imageURL!),
                              ),
                              kwidth10,
                              Expanded(
                                flex: 8,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      width: Get.width * 0.3,
                                      child: Text(
                                        controller.myCartItems[index].name
                                            .toString()
                                            .capitalize!,
                                        style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w700),
                                      ),
                                    ),
                                    kheight5,
                                    Row(
                                      children: [
                                        Text(
                                          getweight(controller
                                              .myCartItems[index].weight
                                              .toString()),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            color: kblack.withOpacity(0.5),
                                          ),
                                        ),
                                        kwidth5,
                                        discountVal > 0
                                            ? Container(
                                                decoration: BoxDecoration(
                                                  color:
                                                      const Color(0xffFF5454),
                                                  borderRadius:
                                                      BorderRadius.circular(2),
                                                ),
                                                child: Text(
                                                  "${discountVal.round()}% OFF",
                                                  style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 10,
                                                      fontWeight:
                                                          FontWeight.w700,
                                                      letterSpacing: -0.5),
                                                  textAlign: TextAlign.center,
                                                ).paddingAll(3),
                                              )
                                            : const SizedBox(),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              Column(
                                children: [
                                  Text(
                                    '${double.parse(controller.myCartItems[index].price.toString()).round()}',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w700,
                                        fontSize: 18),
                                  ),
                                  kwidth10,
                                  Text(
                                    controller.myCartItems[index].mrp ==
                                                "0.00" ||
                                            controller.myCartItems[index].mrp ==
                                                null ||
                                            controller.myCartItems[index].mrp ==
                                                controller
                                                    .myCartItems[index].price
                                        ? ""
                                        : '${double.parse(controller.myCartItems[index].mrp.toString()).round()}',
                                    style: const TextStyle(
                                        decoration: TextDecoration.lineThrough),
                                  ),
                                ],
                              ),
                              kwidth20,
                              Flexible(
                                flex: 5,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    GetBuilder<CartController>(
                                      init: CartController(),
                                      initState: (_) {},
                                      builder: (controller) {
                                        return controller.myCartItems[index]
                                                    .isFree ==
                                                1
                                            ? Container(
                                                decoration: BoxDecoration(
                                                  color: kblack,
                                                  borderRadius:
                                                      BorderRadius.circular(2),
                                                ),
                                                height: 34,
                                                child: Row(
                                                  children: [
                                                    const Text(
                                                      "Free",
                                                      style: TextStyle(
                                                          color: Colors.white),
                                                    ).paddingSymmetric(
                                                        horizontal: 20),
                                                  ],
                                                ),
                                              )
                                            : Expanded(
                                                flex: 3,
                                                child: Container(
                                                  height: 34,
                                                  decoration: BoxDecoration(
                                                    color: kblack,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            2),
                                                  ),
                                                  child: Center(
                                                      child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceAround,
                                                    children: [
                                                      SizedBox(
                                                        width: 25,
                                                        child: IconButton(
                                                          padding:
                                                              EdgeInsets.zero,
                                                          onPressed: (() async {
                                                            if (controller
                                                                    .myCartItems[
                                                                        index]
                                                                    .qty ==
                                                                1) {
                                                              HapticFeedback
                                                                  .vibrate();
                                                              await DatabaseHelper
                                                                  .instance
                                                                  .remove(controller
                                                                      .myCartItems[
                                                                          index]
                                                                      .variantID);
                                                              if (controller
                                                                  .myCartItems
                                                                  .where((element) =>
                                                                      element.isFree ==
                                                                          null ||
                                                                      element.isFree ==
                                                                          0)
                                                                  .isNotEmpty) {
                                                                clearCoupon();
                                                              }
                                                              getProducts();
                                                            } else {
                                                              await DatabaseHelper.instance.decreseQty(ShopingCart(
                                                                  qty: controller
                                                                          .myCartItems[
                                                                              index]
                                                                          .qty! -
                                                                      1,
                                                                  variantID: controller
                                                                      .myCartItems[
                                                                          index]
                                                                      .variantID,
                                                                  productID: controller
                                                                      .myCartItems[
                                                                          index]
                                                                      .productID));
                                                              getProducts();
                                                            }

                                                            Future.delayed(
                                                                const Duration(
                                                                    seconds: 2),
                                                                () async {
                                                              var ord =
                                                                  await DatabaseHelper
                                                                      .instance
                                                                      .getDbOrder2();
                                                              if (ord
                                                                  .isNotEmpty) {
                                                                if (double.parse(configModel
                                                                        .minOrderValue
                                                                        .toString()) >
                                                                    double.parse(ord
                                                                        .first
                                                                        .subTotal
                                                                        .toString())) {
                                                                  clearCoupon();
                                                                  print(
                                                                      'coupon cleared');
                                                                } else {
                                                                  print(
                                                                      'noting will happen');
                                                                }
                                                              }
                                                            });
                                                          }),
                                                          icon: const Icon(
                                                            Icons.remove,
                                                            size: 14,
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                      ),
                                                      Text(
                                                        controller.myCartItems
                                                            .value[index].qty
                                                            .toString(),
                                                        style:
                                                            GoogleFonts.dmSans(
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 25,
                                                        child: IconButton(
                                                          padding:
                                                              EdgeInsets.zero,
                                                          onPressed: () async {
                                                            print('add item');
                                                            await DatabaseHelper.instance.increseQty(ShopingCart(
                                                                qty: controller
                                                                        .myCartItems[
                                                                            index]
                                                                        .qty! +
                                                                    1,
                                                                variantID: controller
                                                                    .myCartItems[
                                                                        index]
                                                                    .variantID,
                                                                productID: controller
                                                                    .myCartItems[
                                                                        index]
                                                                    .productID));
                                                            getProducts();
                                                          },
                                                          icon: const Icon(
                                                            Icons.add,
                                                            size: 14,
                                                            color: Colors.white,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  )),
                                                ));
                                      },
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          kheight10,
          SizedBox(
            width: double.infinity,
            height: 1,
            child: Container(
              decoration: BoxDecoration(
                  gradient: LinearGradient(begin: Alignment.topLeft, colors: [
                kgrey.withOpacity(0.05),
                kgrey.withOpacity(0.1),
                kgrey.withOpacity(0.2),
                kgrey.withOpacity(0.3),
                kgrey.withOpacity(0.4),
                kgrey.withOpacity(0.3),
                kgrey.withOpacity(0.2),
                kgrey.withOpacity(0.1),
                kgrey.withOpacity(0.05),
              ])),
            ),
          ),
          index == controller.myCartItems.length - 1
              ? kheight20
              : const SizedBox()
        ],
      ),
    );
  }

  Future clearCoupon() async {
    await DatabaseHelper.instance.setDiscountValue(0, 0, 0, '');
    await DatabaseHelper.instance.removeFreeItem(); // to remove free item
    setState(() {
      couponText = '';
    });
    print('cleared coupon!');
    getProducts();
  }
}

class PriceRowsWidget extends StatelessWidget {
  const PriceRowsWidget({Key? key, required this.title, required this.amount})
      : super(key: key);
  final String title, amount;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Text(
          amount,
          style: const TextStyle(fontWeight: FontWeight.w600),
        )
      ],
    );
  }
}

class CommonListTile extends StatelessWidget {
  const CommonListTile({
    Key? key,
    required this.text,
    required this.leadingicon,
    required this.onpress,
  }) : super(key: key);
  final String text;
  final VoidCallback onpress;
  final Widget leadingicon;
  @override
  Widget build(BuildContext context) {
    return ListTile(
      minVerticalPadding: 20,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      tileColor: kwhite,
      horizontalTitleGap: 20,
      minLeadingWidth: 0,
      onTap: onpress,
      dense: true,
      trailing: SvgPicture.asset('assets/svg/forwardangle.svg')
          .paddingOnly(right: 14),
      leading: leadingicon,
      title: Text(
        text,
        style: const TextStyle(
            color: kblack, fontSize: 16, fontWeight: FontWeight.w600),
      ),
    );
  }
}

getweight(weight) {
  return double.parse(weight) < 1000
      ? "${double.parse(weight).toInt()} gm"
      : "${(double.parse(weight) ~/ 1000).toInt()} Kg";
}

class SubscribtionCartCard extends StatelessWidget {
  const SubscribtionCartCard({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          const EdgeInsets.symmetric(horizontal: defaultpadding, vertical: 6),
      child: SizedBox(
        child: Card(
          elevation: 8,
          shadowColor: kblack.withOpacity(0.15),
          child: Container(
            color: kwhite,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  color: kblack.withOpacity(0.06),
                  child: Row(
                    children: [
                      Text(
                        'Subscription',
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: kblack.withOpacity(0.6)),
                      ).paddingSymmetric(horizontal: 16, vertical: 8)
                    ],
                  ),
                ),
                SizedBox(
                  height: 150,
                  child: Stack(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            height: 47,
                            child: Stack(
                              children: [
                                SvgPicture.asset(
                                  'assets/svg/triangle_offer.svg',
                                  color: Colors.red,
                                  height: 47,
                                ),
                                Positioned(
                                  bottom: 20,
                                  right: 4,
                                  left: 5,
                                  child: Transform.rotate(
                                      angle: 5.54,
                                      child: const Text(
                                        '30% OFF',
                                        style: TextStyle(
                                            color: kwhite,
                                            fontWeight: FontWeight.w700,
                                            fontSize: 10),
                                      )),
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                              onPressed: () {},
                              icon: const Icon(
                                Icons.close,
                                color: kgrey,
                                size: 30,
                                semanticLabel: 'Remove from Cart',
                              ))
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Expanded(
                              flex: 2,
                              child: SizedBox(
                                  child: Image.asset(
                                'assets/images/milk.png',
                              )),
                            ),
                            Expanded(
                              flex: 5,
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Cow Milk',
                                    style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w700),
                                  ),
                                  kheight5,
                                  Row(
                                    children: [
                                      Text(
                                        '1L',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          color: kblack.withOpacity(0.5),
                                        ),
                                      ),
                                      kwidth10,
                                      Icon(
                                        Icons.circle,
                                        size: 5,
                                        color: kblack.withOpacity(0.5),
                                      ),
                                      kwidth10,
                                      Text(
                                        '₹36',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          color: kblack.withOpacity(0.5),
                                        ),
                                      ),
                                      kwidth10,
                                      Text(
                                        '₹50',
                                        style: TextStyle(
                                          decoration:
                                              TextDecoration.lineThrough,
                                          fontWeight: FontWeight.w400,
                                          color: kblack.withOpacity(0.5),
                                        ),
                                      ),
                                    ],
                                  ),
                                  hgap(8),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          '3 days/week for next 2 months (06 July, 2021)',
                                          style: TextStyle(
                                              color: kblack.withOpacity(0.5)),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Expanded(
                                        flex: 4,
                                        child: Row(
                                          children: [
                                            Text(
                                              '₹364',
                                              style: TextStyle(
                                                  fontWeight: FontWeight.w700,
                                                  fontSize: 18),
                                            ),
                                            kwidth10,
                                            Text(
                                              '₹520',
                                              style: TextStyle(
                                                  decoration: TextDecoration
                                                      .lineThrough),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Expanded(
                                        flex: 3,
                                        child: Container(
                                          color: Colors.black,
                                          child: Center(
                                              child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 6, horizontal: 8),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                const Icon(
                                                  Icons.remove,
                                                  color: Colors.white,
                                                ),
                                                Text(
                                                  "1",
                                                  style: GoogleFonts.dmSans(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.w500,
                                                    color: Colors.white,
                                                  ),
                                                ),
                                                const Icon(
                                                  Icons.add,
                                                  color: Colors.white,
                                                ),
                                              ],
                                            ),
                                          )),
                                        ),
                                      ),
                                    ],
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Divider(
                    thickness: 1,
                    height: 1,
                    color: kblack.withOpacity(0.06),
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Text(
                    'Starting on 06 May, 2021',
                    style: TextStyle(
                        color: kblack.withOpacity(0.7),
                        fontWeight: FontWeight.w400),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

SizedBox _CartCardShimmer() {
  return SizedBox(
    child: Shimmer.fromColors(
      baseColor: Colors.grey[200]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 130,
        width: double.infinity,
        color: Colors.grey,
      ),
    ),
  );
}
