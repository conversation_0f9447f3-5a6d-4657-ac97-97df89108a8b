import 'package:flutter/src/foundation/key.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:rapsap/view/screens/login/widgets/button.dart';
import 'package:rapsap/view/widgets/commons.dart';

class SubscriptionCommingSoon extends StatelessWidget {
  const SubscriptionCommingSoon({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        foregroundColor: kblue,
        backgroundColor: Get.theme.scaffoldBackgroundColor,
      ),
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              "assets/svg/subscribtion.svg",
              fit: BoxFit.cover,
              color: kblack.withOpacity(0.3),
              height: 125,
            ),
            kheight50,
            Text(
              "Subscription Coming soon !",
              style: TextStyle(
                  color: kblack, fontSize: 24, fontWeight: FontWeight.w700),
            ),
            SubmitButton(
                text: 'Come back later',
                onpress: () {
                  Get.back();
                }).paddingAll(24),
            SizedBox(
              height: 50,
            )
          ],
        ),
      ),
    );
  }
}
