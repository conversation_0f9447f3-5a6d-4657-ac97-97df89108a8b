
import 'dart:ui';

import 'package:shimmer/shimmer.dart';

import '../../../../controllers/accountscontroller.dart';
import '../../../../controllers/home_view_controller.dart';
import '../../../../controllers/wishlistcontroller.dart';
import '../../../../main.dart';
import '../../../widgets/commons.dart';

class ShimmerHomeScreen extends StatelessWidget {
  const ShimmerHomeScreen({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeViewController>();
    final Wishlistcontroller wishlistcontroller = Get.find();

    return RefreshIndicator(
      onRefresh: () {
        print("StoreID=${storage.read('storeID').runtimeType}");
        wishlistcontroller.getwishlistitems();
        final AccountController accountController =
            Get.isRegistered<AccountController>()
                ? Get.find<AccountController>()
                : Get.put(AccountController());

        return controller.getHomePageViews();
      },
      child: Scaffold(
          body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: SafeArea(
              child: Column(
                children: [
                  ClipRRect(
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: Container(
                        decoration: BoxDecoration(
                          color: kwhite.withOpacity(0.6),
                        ),
                        height: 30,
                        width: double.infinity,
                      ),
                    ),
                  ),
                  kheight20,
                  Row(
                    children: [
                      Expanded(
                          flex: 4,
                          child: ClipRRect(
                            child: BackdropFilter(
                              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: kwhite.withOpacity(0.6),
                                ),
                                height: 40,
                              ),
                            ),
                          )),
                      kwidth10,
                      Expanded(
                        flex: 2,
                        child: ClipRRect(
                            child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                          child: Container(
                            decoration: BoxDecoration(
                              color: kwhite.withOpacity(0.6),
                            ),
                            height: 40,
                          ),
                        )),
                      ),
                    ],
                  ),
                  kheight30,
                  Container(
                    decoration: BoxDecoration(
                      color: kwhite.withOpacity(0.6),
                    ),
                    height: 150,
                    width: double.infinity,
                  ),
                  kheight20,
                  GridView.builder(
                      itemCount: 15,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                              mainAxisSpacing: 10,
                              crossAxisSpacing: 10,
                              mainAxisExtent: 138,
                              crossAxisCount: 3),
                      itemBuilder: (context, index) {
                        return ClipRRect(
                          child: BackdropFilter(
                              filter: ImageFilter.blur(sigmaX: 40, sigmaY: 40),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: kwhite.withOpacity(0.6),
                                ),
                                height: 138,
                              )),
                        );
                      }),
                ],
              ).paddingAll(defaultpadding),
            )),
      )),
    );
  }
}