import 'package:rapsap/view/screens/ordersection/orderdetail.dart';
import 'package:rapsap/view/screens/product_screen/product_screen.dart';
import 'package:rapsap/view/widgets/commons.dart';

import '../../../model/order/orderdetailmodel/order_detail_model/order_detail.dart';
import '../cart_screen/cart_screen.dart';

class ViewProducts extends StatelessWidget {
  const ViewProducts({Key? key, required this.listitems}) : super(key: key);
  final List<OrderDetail> listitems;

  @override
  Widget build(BuildContext context) {
    // print(listitems[1].toJson());
    return Scaffold(
      appBar: AppBar(
        backgroundColor: kwhite,
        leadingWidth: 0,
        elevation: 0,
        toolbarHeight: 60,
        automaticallyImplyLeading: false,
        title: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                InkWell(
                  onTap: () => Get.back(),
                  child: const Icon(
                    Icons.arrow_back,
                    color: kblue,
                  ),
                ),
                const SizedBox(width: 27),
                SizedBox(
                  width: Get.width * 0.5,
                  child: Text(
                    "View Products",
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.dmSans(
                      fontWeight: FontWeight.w700,
                      fontSize: 20,
                      color: Colors.black,
                    ),
                  ),
                )
              ],
            ),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     getSvgIcon("assets/svg/cart-coverd.svg"),
            //     kwidth10,
            //   ],
            // )
          ],
        ),
        bottom: const PreferredSize(
            child: ThickDivider(), preferredSize: Size.fromHeight(10)),
      ),
      body: ListView.builder(
        itemCount: listitems.length,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return ItemCard(
            data: listitems[index],
            index: index,
          );
        },
      ),
    );
  }
}

class ItemCard extends StatelessWidget {
  final OrderDetail data;
  final int index;
  ItemCard({
    required this.data,
    required this.index,
    Key? key,
  }) : super(key: key);
  // final CartController controller = Get.find<CartController>();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 6),
      child: ClipRRect(
        child: Card(
          elevation: 20,
          shadowColor: kblack.withOpacity(0.2),
          child: Container(
            decoration: BoxDecoration(
              color: kwhite,
              boxShadow: [
                BoxShadow(
                    color: kblack.withOpacity(0.2),
                    blurRadius: 2.0,
                    spreadRadius: 0.4,
                    offset: Offset(0.1, 0.5)),
              ],
            ),
            child: InkWell(
              onTap: () {
                // Get.to(() => ProductScreen(
                //       productId: data.productId!,
                //       variantId: data.variantId,
                //     ));
              },
              child: Column(
                children: [
                  Stack(
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: Padding(
                                    padding: const EdgeInsets.all(10),
                                    child: data.image!.url == null
                                        ? Image.asset(
                                            "assets/images/error-image.png")
                                        : Image.network(data.image!.url!),
                                  ),
                                ),
                                Expanded(
                                  flex: 5,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(
                                        width: Get.width * 0.4,
                                        child: Text(
                                          data.productName!,
                                          style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.w700),
                                        ),
                                      ),
                                      kheight5,
                                      Text(
                                        double.tryParse(data.variantName!
                                                    .split('')
                                                    .first) ==
                                                null
                                            ? data.variantName
                                            : getweight(data.variantName
                                                ?.split(' ')
                                                .first),
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          color: kblack.withOpacity(0.5),
                                        ),
                                      ),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Flexible(
                                            flex: 4,
                                            child: Row(
                                              children: [
                                                Text(
                                                  double.parse(
                                                          data.buyingPrice ??
                                                              data.price ??
                                                              "")
                                                      .round()
                                                      .toString(),
                                                  style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.w700,
                                                      fontSize: 18),
                                                ),
                                                kwidth10,
                                                // Text(
                                                //   data.variants![variant_index]
                                                //                   .mrp ==
                                                //               "0.00" ||
                                                //           data
                                                //                   .variants![
                                                //                       variant_index]
                                                //                   .mrp ==
                                                //               data
                                                //                   .variants![
                                                //                       variant_index]
                                                //                   .price
                                                //       ? ""
                                                //       : '${data.variants![variant_index].mrp}',
                                                //   style: TextStyle(
                                                //       decoration: TextDecoration
                                                //           .lineThrough),
                                                // ),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                              flex: 3,
                                              child: Container(
                                                height: 34,
                                                color: Colors.black,
                                                child: Center(
                                                    child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceAround,
                                                  children: [
                                                    Text(
                                                      "Qty: ${data.quantity}",
                                                      style: GoogleFonts.dmSans(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        color: Colors.white,
                                                      ),
                                                    ),
                                                  ],
                                                )),
                                              )),
                                        ],
                                      )
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     SizedBox(
                      //       height: 47,
                      //       child: Stack(
                      //         children: [
                      //           SvgPicture.asset(
                      //             'assets/svg/triangle_offer.svg',
                      //             color: Color(0xffE73636),
                      //             height: 47,
                      //           ),
                      //           Positioned(
                      //             bottom: 20,
                      //             right: 4,
                      //             left: 3,
                      //             child: Transform.rotate(
                      //                 angle: 5.54,
                      //                 child: const Text(
                      //                   '30% OFF',
                      //                   style: TextStyle(
                      //                       color: kwhite,
                      //                       fontWeight: FontWeight.w700,
                      //                       fontSize: 10),
                      //                 )),
                      //           ),
                      //         ],
                      //       ),
                      //     ),
                      //   ],
                      // ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
