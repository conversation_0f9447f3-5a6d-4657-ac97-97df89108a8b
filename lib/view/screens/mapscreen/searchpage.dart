import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rapsap/utils/constants.dart';

import 'package:rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:rapsap/view/screens/login/widgets/button.dart';
import 'package:rapsap/view/screens/mapscreen/setlocation.dart';
import 'package:rapsap/view/screens/root_page/root_page.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:shimmer/shimmer.dart';

import '../../../controllers/accountscontroller.dart';
import '../../../controllers/home_view_controller.dart';
import '../../../services/databaseHelper.dart';
import '../AddressPage/myaddress.dart';

class MapSearchScreen extends StatefulWidget {
  MapSearchScreen({Key? key}) : super(key: key);

  @override
  State<MapSearchScreen> createState() => _MapSearchScreenState();
}

class _MapSearchScreenState extends State<MapSearchScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  // var googlePlace = GooglePlace("AIzaSyCqidk982_mxHi5xA8pW37E9Yw7PHYtGaU");
  @override
  Widget build(BuildContext context) {
    final AccountController accountController =
        Get.isRegistered<AccountController>()
            ? Get.find<AccountController>()
            : Get.put(AccountController());

    accountController.getaddress();
    // List<AutocompletePrediction> predictions = [];
    return Scaffold(
        appBar: AppBar(
          backgroundColor: kwhite,
          toolbarHeight: 0,
          elevation: 0,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(65),
            child: Column(
              children: [
                Row(
                  children: [
                    Flexible(
                        flex: 1,
                        child: Center(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 12.0),
                            child: IconButton(
                              icon: const Icon(
                                Icons.arrow_back,
                                color: kblue,
                                size: 30,
                              ),
                              onPressed: () {
                                Get.back();
                              },
                            ),
                          ),
                        )),
                    const Flexible(
                        flex: 4,
                        child: Text(
                          'Select Address',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        )),

                    // Flexible(
                    //   flex: 7,
                    //   child: Padding(
                    //     padding: const EdgeInsets.all(20),
                    //     child: SizedBox(
                    //       height: 45,
                    //       child: TextField(
                    //         onTap: () async {
                    //           var result =
                    //               await googlePlace.autocomplete.get("koc");
                    //           if (result != null && result.predictions != null) {
                    //             predictions = result.predictions!;
                    //             print(result.status);
                    //             if (kDebugMode) {
                    //               print(
                    //                   "placesresult=${predictions[0].structuredFormatting}");
                    //             }
                    //           }
                    //         },
                    //         decoration: InputDecoration(
                    //           isDense: true,
                    //           prefixIcon: Padding(
                    //             padding: const EdgeInsets.all(16),
                    //             child:
                    //                 SvgPicture.asset("assets/svg/search-icon.svg"),
                    //           ),
                    //           suffixIcon: Padding(
                    //             padding: const EdgeInsets.all(15),
                    //             child: SvgPicture.asset("assets/svg/voice.svg"),
                    //           ),
                    //           hintStyle: const TextStyle(
                    //               height: 3.25,
                    //               color: Colors.grey,
                    //               fontSize: 16,
                    //               fontWeight: FontWeight.w400),
                    //           hintText: 'Search for Location',
                    //           focusedBorder: const OutlineInputBorder(
                    //             borderSide: BorderSide(
                    //               color: Color(0xffC5C8CD),
                    //             ),
                    //           ),
                    //           enabledBorder: const OutlineInputBorder(
                    //             borderSide: BorderSide(
                    //               width: 1,
                    //               color: Color(0xffC5C8CD),
                    //             ),
                    //           ),
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
                kheight10,
                Divider(
                  color: Color(0xFFF3F4F9),
                  thickness: 4,
                  height: 0,
                ),
              ],
            ),
          ),
        ),
        body: Stack(
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Obx(() => Container(
                    color: kwhite,
                    child: accountController.buttonloading.value
                        ? const SizedBox(
                            height: 56,
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: null,
                              child: SizedBox(
                                height: 20,
                                child: LoadingIndicator(
                                  colors: [kblue, kblack],
                                  indicatorType: Indicator.cubeTransition,
                                ),
                              ),
                            ),
                          ).paddingSymmetric(horizontal: 24, vertical: 10)
                        : SubmitButton(
                                text: 'Continue',
                                onpress: accountController
                                            .selectedAddress.value.phone ==
                                        null
                                    ? null
                                    : () async {
                                        accountController.buttonloading.value =
                                            true;
                                        final HomeViewController controller =
                                            Get.find();
                                        await controller.getHomePageViews();
                                        accountController.buttonloading.value =
                                            false;

                                        Get.to(() => RootPage());
                                      })
                            .paddingSymmetric(horizontal: 24, vertical: 10),
                  )),
            ),
            SingleChildScrollView(
                child: Column(
              children: [
                ListTile(
                  onTap: (() async {
                    final position = await determinePosition();
                    Get.to(() => GoogleMapPage(
                          latLng: LatLng(position.latitude, position.longitude),
                        ));
                  }),
                  dense: true,
                  trailing: const Icon(
                    Icons.arrow_forward_ios_rounded,
                    color: kblack,
                  ),
                  leading: const Icon(
                    Icons.my_location_outlined,
                    color: kblack,
                  ),
                  title: Text(
                    'Use Current Location',
                    style: TextStyle(
                        color: kblack,
                        fontSize: 15,
                        fontFamily: GoogleFonts.inter().fontFamily,
                        fontWeight: FontWeight.w600),
                  ),
                ),
                const Divider(
                  color: Color(0xFFF3F4F9),
                  thickness: 4,
                ),
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ListTile(
                        horizontalTitleGap: 0,
                        minLeadingWidth: 0,
                        title: const Padding(
                          padding: EdgeInsets.only(left: 8, right: 2),
                          child: Text(
                            'Add new Address',
                            style: TextStyle(fontSize: 16),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        leading: const Padding(
                            padding: EdgeInsets.only(left: 14),
                            child: Icon(
                              Icons.add_circle_outline_outlined,
                              size: 20,
                              color: kblack,
                            )),
                        trailing: GestureDetector(
                          onTap: () {
                            Get.to(() => AddressAddEditPage(
                                  mode: addEditAddress.add,
                                ));
                          },
                          child: Container(
                            decoration: BoxDecoration(
                                border: Border.all(color: kblue),
                                borderRadius: BorderRadius.circular(4)),
                            child: const Text(
                              'Add Address',
                              style: TextStyle(color: kblue),
                            ).paddingAll(
                              8,
                            ),
                          ),
                        ),
                      ),
                      CommonDivider(),
                      const Text(
                        'Saved Address',
                        style: TextStyle(
                            color: kblack,
                            fontWeight: FontWeight.w600,
                            fontSize: 18),
                      ).paddingSymmetric(horizontal: 24),
                      kheight20,
                      Obx(() =>
                          accountController.addressModel.value.data == null
                              ? Shimmer.fromColors(
                                  baseColor: Colors.grey[200]!,
                                  highlightColor: Colors.grey[100]!,
                                  child: Container(
                                    height: 80,
                                    width: double.infinity,
                                    color: Colors.grey,
                                  ).paddingSymmetric(horizontal: 20),
                                )
                              : SelectAddressWidget()),

                      kheight20,
                      // SelectAddressWidget(),
                      // Divider(
                      //   height: 25,
                      //   thickness: 1,
                      //   color: Color(0xFFF3F4F9),
                      // ),
                      // SelectAddressWidget(),
                      // kheight5,
                      // Divider(
                      //   thickness: 1,
                      //   color: Color(0xFFF3F4F9),
                      // ),
                    ],
                  ),
                ]),
              ],
            )),
          ],
        ));
  }
}

class CommonDivider extends StatelessWidget {
  const CommonDivider({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Divider(
      color: Color(0xFFF3F4F9),
      thickness: 4,
    );
  }
}

class SelectAddressWidget extends StatefulWidget {
  SelectAddressWidget({
    Key? key,
  }) : super(key: key);

  final AccountController accountController = Get.find();

  @override
  State<SelectAddressWidget> createState() => _SelectAddressWidgetState();
}

class _SelectAddressWidgetState extends State<SelectAddressWidget> {
  @override
  Widget build(BuildContext context) {
    if (widget.accountController.addressModel.value.data!.isEmpty) {
      return Column(
        children: [
          SizedBox(
            height: 70,
          ),
          Center(child: Text('No saved address'))
        ],
      );
    }
    int selectedvlue = widget
        .accountController
        .addressModel
        .value
        .data![widget.accountController.addressModel.value.data!
                    .indexWhere((element) => element.isDefault == 1) !=
                -1
            ? widget.accountController.addressModel.value.data!
                .indexWhere((element) => element.isDefault == 1)
            : 0]
        .addressId!;

    selectedAddress = widget.accountController.addressModel.value.data![widget
                .accountController.addressModel.value.data!
                .indexWhere((element) => element.isDefault == 1) !=
            -1
        ? widget.accountController.addressModel.value.data!
            .indexWhere((element) => element.isDefault == 1)
        : 0];
    return StatefulBuilder(builder: (context, StateSetter setState) {
      print(widget.accountController.addressModel.value.data!.length);
      return ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: widget.accountController.addressModel.value.data!.length,
          itemBuilder: (context, index) {
            final model =
                widget.accountController.addressModel.value.data![index];
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: (() {
                    setState(() {
                      selectedvlue = model.addressId!;
                      selectedAddress = model;
                    });
                    widget.accountController.selectedAddress.value = model;
                  }),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            width: 15,
                            height: 20,
                            child: Radio<int?>(
                              value: model.addressId,
                              groupValue: selectedvlue,
                              onChanged: (value) {
                                setState(() {
                                  selectedvlue = model.addressId!;
                                  selectedAddress = model;
                                });
                              },
                              fillColor: MaterialStateProperty.all(kblack),
                            ),
                          ),
                          kwidth10,
                          Text(
                            model.name!,
                            style: const TextStyle(fontWeight: FontWeight.w600),
                          ),
                          kwidth20,
                          Container(
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                color: kblue.withOpacity(0.15)),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8.0, vertical: 4),
                              child: Text(
                                model.addressType!,
                                style: TextStyle(
                                    fontSize: 10,
                                    color: kblue,
                                    fontWeight: FontWeight.w400),
                              ),
                            ),
                          ),
                          Spacer(),
                          InkWell(
                            onTap: () {
                              Get.to(() => AddressAddEditPage(
                                    mode: addEditAddress.edit,
                                    address: model,
                                  ));
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                  border: Border.all(color: kblue),
                                  borderRadius: BorderRadius.circular(3)),
                              child: const Text(
                                'Edit',
                                style: TextStyle(color: kblue),
                              ).paddingSymmetric(
                                horizontal: 4,
                                vertical: 2,
                              ),
                            ),
                          ),
                        ],
                      ),
                      kheight5,
                      Text(
                        "${model.address1}, ${model.address2}, ${model.landmark},${model.city == "" ? "" : " ${model.city},"}${model.state == "" ? "" : " ${model.state},"} ${model.pincode}, ${model.phone}",
                        style: TextStyle(color: Color(0xFF556F80)),
                        textAlign: TextAlign.left,
                      ),
                    ],
                  ).paddingSymmetric(horizontal: 24),
                ),
                const Divider(),
              ],
            );
          });
    });
  }
}

Future<Position> determinePosition() async {
  bool serviceEnabled;
  LocationPermission permission;

  // Test if location services are enabled.
  serviceEnabled = await Geolocator.isLocationServiceEnabled();
  if (!serviceEnabled) {
    // Location services are not enabled don't continue
    // accessing the position and request users of the
    // App to enable the location services.

    customToast(message: "Location service disabled");

    return Future.error('Location services are disabled.');
  }

  permission = await Geolocator.checkPermission();
  if (permission == LocationPermission.denied) {
    permission = await Geolocator.requestPermission();
    if (permission == LocationPermission.denied) {
      print(permission);

      customToast(message: "Location permission Denied");

      // Permissions are denied, next time you could try
      // requesting permissions again (this is also where
      // Android's shouldShowRequestPermissionRationale
      // returned true. According to Android guidelines
      // your App should show an explanatory UI now.
      return Future.error('Location permissions are denied');
    }
  }

  if (permission == LocationPermission.deniedForever) {
    print(permission);

    Future.delayed(
        Duration(milliseconds: 1000), (() => Geolocator.openAppSettings()));
    // Permissions are denied forever, handle appropriately.
    return Future.error(
        'Location permissions are permanently denied, we cannot request permissions.');
  }

  // When we reach here, permissions are granted and we can
  // continue accessing the position of the device.
  return await Geolocator.getCurrentPosition(
    desiredAccuracy: LocationAccuracy.best,
  );
}

//old code

// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';

// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:get/get.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:google_place/google_place.dart';
// import 'package:rapsap/utils/constants.dart';
// import 'package:rapsap/controllers/accountscontroller.dart';
// import 'package:rapsap/model/address_model/address_model/address_model.dart';
// import 'package:rapsap/model/address_model/address_model/datum.dart';
// import 'package:rapsap/view/screens/AddressPage/addaddresspage.dart';
// import 'package:rapsap/view/screens/mapscreen/setlocation.dart';

// class MapSearchScreen extends StatelessWidget {
//   MapSearchScreen({Key? key}) : super(key: key);
//   var googlePlace = GooglePlace("AIzaSyCqidk982_mxHi5xA8pW37E9Yw7PHYtGaU");

//   @override
//   Widget build(BuildContext context) {
//     List<AutocompletePrediction> predictions = [];
//     return Scaffold(
//         body: SingleChildScrollView(
//       child: SafeArea(
//         child: Column(
//           children: [
//             Row(
//               children: [
//                 Flexible(
//                     flex: 1,
//                     child: Center(
//                       child: Padding(
//                         padding: const EdgeInsets.only(left: 12.0),
//                         child: IconButton(
//                           icon: Icon(
//                             Icons.arrow_back,
//                             color: kblue,
//                             size: 30,
//                           ),
//                           onPressed: () {
//                             Get.back();
//                           },
//                         ),
//                       ),
//                     )),
//                 Flexible(
//                   flex: 7,
//                   child: Padding(
//                     padding: const EdgeInsets.all(20),
//                     child: SizedBox(
//                       height: 45,
//                       child: TextField(
//                         onTap: () async {
//                           var result =
//                               await googlePlace.autocomplete.get("koc");
//                           if (result != null && result.predictions != null) {
//                             predictions = result.predictions!;
//                             print(result.status);
//                             if (kDebugMode) {
//                               print(
//                                   "placesresult=${predictions[0].structuredFormatting}");
//                             }
//                           }
//                         },
//                         decoration: InputDecoration(
//                           isDense: true,
//                           prefixIcon: Padding(
//                             padding: const EdgeInsets.all(16),
//                             child:
//                                 SvgPicture.asset("assets/svg/search-icon.svg"),
//                           ),
//                           suffixIcon: Padding(
//                             padding: const EdgeInsets.all(15),
//                             child: SvgPicture.asset("assets/svg/voice.svg"),
//                           ),
//                           hintStyle: const TextStyle(
//                               height: 3.25,
//                               color: Colors.grey,
//                               fontSize: 16,
//                               fontWeight: FontWeight.w400),
//                           hintText: 'Search for Location',
//                           focusedBorder: const OutlineInputBorder(
//                             borderSide: BorderSide(
//                               color: Color(0xffC5C8CD),
//                             ),
//                           ),
//                           enabledBorder: const OutlineInputBorder(
//                             borderSide: BorderSide(
//                               width: 1,
//                               color: Color(0xffC5C8CD),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             CommonDivider(),
//             Column(
//               children: [
//                 ListTile(
//                   onTap: (() async {
//                     final position = await determinePosition();
//                     Get.to(() => GoogleMapPage(
//                           latLng: LatLng(position.latitude, position.longitude),
//                         ));
//                   }),
//                   dense: true,
//                   trailing: const Icon(
//                     Icons.arrow_forward_ios_rounded,
//                     color: kblack,
//                   ),
//                   leading: const Icon(
//                     Icons.my_location_outlined,
//                     color: kblack,
//                   ),
//                   title: Text(
//                     'Use Current Location',
//                     style: TextStyle(
//                         color: kblack,
//                         fontSize: 15,
//                         fontFamily: GoogleFonts.inter().fontFamily,
//                         fontWeight: FontWeight.w600),
//                   ),
//                 ),
//                 const Divider(
//                   color: Color(0xFFF3F4F9),
//                   thickness: 4,
//                 ),
//                 kheight20,
//                 Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 20.0),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       const Text(
//                         'Saved Address',
//                         style: TextStyle(
//                             color: kblack,
//                             fontWeight: FontWeight.w600,
//                             fontSize: 18),
//                       ),
//                       kheight20,
//                       // SelectAddressWidget(),
//                       // Divider(
//                       //   height: 25,
//                       //   thickness: 1,
//                       //   color: Color(0xFFF3F4F9),
//                       // ),
//                       // SelectAddressWidget(),
//                       // kheight5,
//                       Divider(
//                         thickness: 1,
//                         color: Color(0xFFF3F4F9),
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             )
//           ],
//         ),
//       ),
//     ));
//   }
// }

// class CommonDivider extends StatelessWidget {
//   const CommonDivider({
//     Key? key,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return const Divider(
//       color: Color(0xFFF3F4F9),
//       thickness: 4,
//     );
//   }
// }

// class SelectAddressWidget extends StatefulWidget {
//   SelectAddressWidget({
//     this.selectedvlue,
//     this.model,
//     Key? key,
//     this.index = 0,
//   }) : super(key: key);
//   final int index;
//   int? selectedvlue;
//   final Datum? model;

//   @override
//   State<SelectAddressWidget> createState() => _SelectAddressWidgetState();
// }

// class _SelectAddressWidgetState extends State<SelectAddressWidget> {
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         Row(
//           children: [
//             SizedBox(
//               width: 15,
//               height: 20,
//               child: Radio<int?>(
//                 value: widget.model!.addressId,
//                 groupValue: widget.selectedvlue,
//                 onChanged: (value) {
//                   setState(() {
//                     widget.selectedvlue = value;
//                   });
//                 },
//                 fillColor: MaterialStateProperty.all(kblack),
//               ),
//             ),
//             kwidth10,
//             Text(
//               widget.model!.name!,
//               style: TextStyle(fontWeight: FontWeight.w600),
//             ),
//             kwidth20,
//             Container(
//               decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(4),
//                   color: kblue.withOpacity(0.15)),
//               child: Padding(
//                 padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
//                 child: Text(
//                   widget.model!.addressType!,
//                   style: TextStyle(
//                       fontSize: 10, color: kblue, fontWeight: FontWeight.w400),
//                 ),
//               ),
//             ),
//             Spacer(),
//             InkWell(
//               onTap: () {
//                 Get.to(() => AddressAddEditPage(
//                       mode: addEditAddress.edit,
//                       address: widget.model,
//                     ));
//               },
//               child: Container(
//                 decoration: BoxDecoration(
//                     border: Border.all(color: kblue),
//                     borderRadius: BorderRadius.circular(3)),
//                 child: const Text(
//                   'Edit',
//                   style: TextStyle(color: kblue),
//                 ).paddingSymmetric(
//                   horizontal: 4,
//                   vertical: 2,
//                 ),
//               ),
//             ),
//           ],
//         ),
//         kheight5,
//         Text(
//           "${widget.model!.address1}, ${widget.model!.address2}, ${widget.model!.landmark},${widget.model!.city},${widget.model!.state},${widget.model!.pincode}, ${widget.model!.phone}",
//           style: TextStyle(color: Color(0xFF556F80)),
//         ),
//       ],
//     );
//   }
// }

// Future<Position> determinePosition() async {
//   bool serviceEnabled;
//   LocationPermission permission;

//   // Test if location services are enabled.
//   serviceEnabled = await Geolocator.isLocationServiceEnabled();
//   if (!serviceEnabled) {
//     // Location services are not enabled don't continue
//     // accessing the position and request users of the
//     // App to enable the location services.

//     return Future.error('Location services are disabled.');
//   }

//   permission = await Geolocator.checkPermission();
//   if (permission == LocationPermission.denied) {
//     permission = await Geolocator.requestPermission();
//     if (permission == LocationPermission.denied) {
//       // Permissions are denied, next time you could try
//       // requesting permissions again (this is also where
//       // Android's shouldShowRequestPermissionRationale
//       // returned true. According to Android guidelines
//       // your App should show an explanatory UI now.
//       return Future.error('Location permissions are denied');
//     }
//   }

//   if (permission == LocationPermission.deniedForever) {
//     // Permissions are denied forever, handle appropriately.
//     return Future.error(
//         'Location permissions are permanently denied, we cannot request permissions.');
//   }

//   // When we reach here, permissions are granted and we can
//   // continue accessing the position of the device.
//   return await Geolocator.getCurrentPosition(
//     desiredAccuracy: LocationAccuracy.best,
//   );
// }
