import 'dart:ui';

import 'package:rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:rapsap/view/screens/root_page/root_page.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rapsap/controllers/accountscontroller.dart';
import 'package:rapsap/view/screens/login/widgets/button.dart';
import 'package:rapsap/view/screens/mapscreen/searchpage.dart';
import 'package:rapsap/view/screens/mapscreen/setlocation.dart';
import 'package:rapsap/view/widgets/custom.dart';
import 'package:loading_indicator/loading_indicator.dart';

import '../../../controllers/home_view_controller.dart';
import '../../../services/databaseHelper.dart';
import '../../../services/userservices.dart';
import '../../widgets/commons.dart';

class MapPage extends StatefulWidget {
  @override
  _MapPageState createState() => _MapPageState();
}

class _MapPageState extends State<MapPage> {
  late String pincode = '';
  bool stateloading = false;
  final UserController userController = Get.find();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: kwhite,
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              // flex: ,
              child: Container(
                color: Colors.white,
                child: SvgPicture.asset(
                  'assets/svg/locationheadimage.svg',
                  fit: BoxFit.scaleDown,
                ),
              ),
            ),
            Expanded(
                // flex: 2,
                child: Container(
              color: Colors.white,
              // width: double.infinity,
              child: Padding(
                padding: const EdgeInsets.only(left: 18, right: 18),
                child: Column(
                  children: [
                    const Text(
                      'Hi, nice to meet you',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    hgap(15),
                    const Text(
                      'Please set your delivery location',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                    ),
                    kheight30,
                    stateloading
                        ? ElevatedButton(
                            onPressed: null,
                            style: ElevatedButton.styleFrom(
                              minimumSize: const Size(double.infinity, 56),
                              backgroundColor: kblack,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: const [
                                SizedBox(
                                  height: 15,
                                  width: 15,
                                  child: CircularProgressIndicator.adaptive(
                                    strokeWidth: 3,
                                    backgroundColor: kgrey,
                                    valueColor:
                                        AlwaysStoppedAnimation<Color>(kblack),
                                  ),
                                ),
                                kwidth10,
                                Text("Getting Location",
                                    style: TextStyle(
                                        color: kwhite,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w900)),
                              ],
                            ),
                          )
                        : SubmitButton(
                            text: 'Use Current Location',
                            onpress: () async {
                              final AccountController accountController =
                                  Get.isRegistered<AccountController>()
                                      ? Get.find<AccountController>()
                                      : Get.put(AccountController());
                              getlocation();
                            }),
                    // Spacer(),
                    const SizedBox(height: 20),
                    SubmitButton(
                      text: 'Set Your Location Manually',
                      onpress: () {
                        // showModalBottomSheet(
                        //     context: context,
                        //     builder: (context) {
                        //       return Container(
                        //         height: 200,
                        //       );
                        //     });
                        showPincodeDialog(
                            context, 'Enter Pincode to check Servicebality');
                        // final AccountController accountController =
                        //     Get.isRegistered<AccountController>()
                        //         ? Get.find<AccountController>()
                        //         : Get.put(AccountController());

                        // // Get.to(() => MapSearchScreen());
                      },
                      side: Border.all(color: kblack, width: 2),
                      bgcolor: kwhite,
                      txtcolor: kblack,
                    ),
                  ],
                ),
              ),
            )),
            // Expanded(
            //   flex: 1,
            //   child: Container(),
            // )
          ],
        ));
  }

  showPincodeDialog(BuildContext context, String title) {
    TextEditingController textEditingController = TextEditingController();
    pincode = "";

    // set up the AlertDialog
    Dialog alert = Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      // title: Text(title),
      child: Padding(
        padding: const EdgeInsets.all(defaultpadding),
        child: StatefulBuilder(builder: (context, setState) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              kheight10,
              const Text(
                'Check the serviceablity',
                style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: 18,
                    letterSpacing: -0.25),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  kheight20,
                  const Text(
                    'Enter the pincode',
                    style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        letterSpacing: -0.25),
                  ),
                  kheight10,
                  SizedBox(
                    height: 45,
                    child: TextField(
                      onChanged: ((value) {
                        if (value.length >= 6) {
                          setState((() {
                            pincode = value;
                          }));
                          checkServicableFn(value);
                        }
                      }),
                      selectionHeightStyle: BoxHeightStyle.max,
                      controller: textEditingController,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9]'))
                      ],
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        contentPadding: EdgeInsets.symmetric(horizontal: 20),
                        counterText: "",
                        fillColor: Color(0xfff4f4f4),
                        filled: true,
                        hintText: 'Pincode',
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: kblack, width: 1),
                          borderRadius: BorderRadius.all(
                            Radius.circular(4),
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.all(
                            Radius.circular(4),
                          ),
                        ),
                        border: OutlineInputBorder(
                          borderSide: BorderSide.none,
                          borderRadius: BorderRadius.all(
                            Radius.circular(4),
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderSide:
                              BorderSide(color: Colors.redAccent, width: 2.0),
                          borderRadius: BorderRadius.all(
                            Radius.circular(4),
                          ),
                        ),
                      ),
                      maxLengthEnforcement: MaxLengthEnforcement.enforced,
                      maxLength: 6,
                    ),
                  ),
                ],
              ),
              kheight10,
              kheight20,
              userController.serviceloading.value
                  ? SizedBox(
                      height: 45,
                      width: double.infinity,
                      child: const ElevatedButton(
                        onPressed: null,
                        child: SizedBox(
                          height: 20,
                          child: LoadingIndicator(
                            colors: [kblue, kblack],
                            indicatorType: Indicator.cubeTransition,
                          ),
                        ),
                      ).paddingSymmetric(horizontal: 30))
                  : SubmitButton(
                      height: 45,
                      text: 'Check Now',
                      onpress: pincode.length < 6
                          ? null
                          : () {
                              checkServicableFn(pincode);
                            },
                      textsize: 16,
                    ).paddingSymmetric(horizontal: 30)
            ],
          );
        }),
      ),
    );

    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  checkServicableFn(String pincode) async {
    final UserController userController = Get.find();
    userController.serviceloading.value = true;

    GetStorage storage = GetStorage();
    int userType = storage.read('userType') ?? 0;
    print('userType $userType');

    var pincodeReq = {
      'pincode': pincode,
      // 'lat': currentPosition.latitude,
      // 'long': currentPosition.longitude,
    };
    var pincodeReqWToken = {
      'pincode': pincode,
      // 'lat': currentPosition.latitude,
      // 'long': currentPosition.longitude,
      "key": "JWT",
      "secret": "RAPSAP",
    };

    var dd = await UserService.checkServiceable(
      userType == 0 ? pincodeReqWToken : pincodeReq,
    );

    print('pincode result $dd');
    if (dd['success'] == true) {
      storage.write('tempPincode', pincode);
      storage.write('place', dd['data']['description']);

      storage.write('storeID', dd['data']['store_id']);
      final HomeViewController controller = Get.find();
      controller.getHomePageViews();

      DatabaseHelper.instance.getGroceries();
      userController.serviceloading.value = false;

      Get.to(() => const RootPage());
    } else {
      userController.serviceloading.value = false;

      Get.close(1);
      showAlertDialog(context, "Pincode is not servicable!",
          "We are increasing our reach everyday. We shall service your locality soon");
    }
    // else {
    //   // Get.to(() => AddressPage(step: 1));
    // }
  }

  getlocation() async {
    try {
      setState(() {
        stateloading = true;
      });
      final position = await determinePosition();
      setState(() {
        stateloading = false;
      });

      Get.to(() => GoogleMapPage(
            latLng: LatLng(position.latitude, position.longitude),
          ));
    } catch (e) {
      setState(() {
        stateloading = false;
        // Get.snackbar('Error getting location', 'Check your location settings',
        //     icon: Icon(Icons.location_off),
        //     colorText: kwhite,
        //     backgroundColor: Colors.redAccent);

        customToast(
            message: "Error getting location, Check your location settings");
      });
    }
  }
}
