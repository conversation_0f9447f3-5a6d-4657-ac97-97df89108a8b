import 'dart:async';

import 'package:rapsap/view/screens/mapscreen/mappage.dart';
import 'package:rapsap/view/widgets/buttonanimation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:geocoder/geocoder.dart';
// import 'package:flutter_geocoder/geocoder.dart';
import 'package:geocoding/geocoding.dart';
import 'package:rapsap/controllers/accountscontroller.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:rapsap/main.dart';
import 'package:rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:rapsap/view/screens/login/widgets/button.dart';
import 'package:marquee/marquee.dart';
import 'package:shimmer/shimmer.dart';

import '../../../controllers/home_view_controller.dart';
import '../../../services/databaseHelper.dart';
import '../../../services/userservices.dart';
import '../root_page/root_page.dart';

enum Pagestep {
  addaddress,
  maplocation,
}

class GoogleMapPage extends StatefulWidget {
  final LatLng latLng;
  Pagestep? page = Pagestep.maplocation;
  GoogleMapPage({
    Key? key,
    required this.latLng,
    this.page,
  }) : super(key: key);

  @override
  _GoogleMapPageState createState() => _GoogleMapPageState();
}

class _GoogleMapPageState extends State<GoogleMapPage> {
  final Completer<GoogleMapController> _controller = Completer();
  final UserController _userController = Get.find<UserController>();
  bool outofService = false;
  bool loading = true;

  final AccountController accountController =
      Get.isRegistered<AccountController>()
          ? Get.find<AccountController>()
          : Get.put(AccountController());

  List<Marker> allMarker = [];

  List<Placemark> addressList = [];
  late LatLng currentPosition;

  CameraPosition _kGooglePlex = const CameraPosition(
    target: LatLng(19.**************, 72.**************),
    zoom: 14,
  );

  //  LatLng(19.***************, 72.**************)

  @override
  void initState() {
    // TODO: implement initState

    super.initState();
    if (kDebugMode) {
      print(widget.latLng.latitude);
    }
    if (widget.latLng.latitude != null && widget.latLng.longitude != null) {
      setState(() {
        currentPosition = widget.latLng;
        _kGooglePlex = CameraPosition(
          target: LatLng(widget.latLng.latitude, widget.latLng.longitude),
          zoom: 17,
        );
      });
      getAddressFromGeo(
          LatLng(widget.latLng.latitude, widget.latLng.longitude));
    } else {
      setState(() {
        _kGooglePlex = const CameraPosition(
          target: LatLng(19.**************, 72.**************),
          zoom: 17,
        );
        currentPosition = const LatLng(19.**************, 72.**************);
      });
      getAddressFromGeo(const LatLng(19.**************, 72.**************));
    }

    allMarker.add(Marker(
      markerId: MarkerId('MyMarker'),
      consumeTapEvents: true,
      // draggable: true,
      // onDragEnd: (LatLng latLng) {
      //   print('Moved here ${latLng}');
      //   setState(() {
      //     currentPosition = latLng;
      //   });
      //   getAddressFromGeo(latLng);
      // },
      position: widget.latLng,
    ));
  }

  getAddressFromGeo(LatLng latLng) async {
    print(latLng);
    // final coordinates = new Coordinates(latLng.latitude, latLng.longitude);
    // List<Location> address =
    //     await Geocoder.local.findAddressesFromCoordinates(coordinates);

    List<Placemark> placemarks =
        await placemarkFromCoordinates(latLng.latitude, latLng.longitude);

    // print(
    //     '${address.first.addressLine} ${address.first.postalCode} ${address.first.locality} ${address.first.adminArea}');

    setState(() {
      addressList = placemarks;
      print(addressList.first.toString());
    });

    var pincodeReqWToken = {
      'pincode': addressList.first.postalCode,
      // 'lat': currentPosition.latitude,
      // 'long': currentPosition.longitude,
      "key": "JWT",
      "secret": "RAPSAP",
    };

    var dd = await UserService.checkServiceable(pincodeReqWToken);
    if (dd['success'] == false) {
      setState(() {
        outofService = true;
        print(outofService);
        loading = false;
        print('out $outofService');
      });
    } else {
      setState(() {
        outofService = false;
        loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    if (storage.read('loginstatus') != null) {
      accountController.getaddress();
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: kwhite,
        elevation: 0.5,
        title: const Text(
          'Select Location',
          style: TextStyle(color: Colors.black87),
        ),
        iconTheme: const IconThemeData(color: Colors.black87),
      ),
      body: Stack(
        fit: StackFit.loose,
        children: [
          Container(
            height: size.height,
            width: size.width,
            child: GoogleMap(
              myLocationEnabled: true,
              myLocationButtonEnabled: true,
              zoomControlsEnabled: true,
              onCameraIdle: () {
                getAddressFromGeo(currentPosition);
              },
              onCameraMove: (position) {
                setState(() {
                  loading = true;
                  allMarker.add(Marker(
                      markerId: const MarkerId('MyMarker'),
                      position: position.target));
                  currentPosition = position.target;
                });
              },
              mapType: MapType.terrain,
              initialCameraPosition: _kGooglePlex,
              onMapCreated: (GoogleMapController controller) {
                _controller.complete(controller);
              },
              markers: Set.from(allMarker),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Column(
              children: [
                outofService == true
                    ? Container(
                        height: 20,
                        width: double.infinity,
                        color: kblue,
                        child: Row(
                          children: [
                            Expanded(
                              child: Marquee(
                                text:
                                    'We are increasing our reach everyday. We shall service your locality soon',
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                    color: kwhite),
                                blankSpace: 100,
                              ),
                            ),
                          ],
                        ))
                    : Container(),
                Container(
                  color: Colors.white,
                  height: 180,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          'Your location',
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.w600),
                        ),
                      ),
                      Spacer(),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Row(
                          children: [
                            SvgPicture.asset(
                                'assets/images/locationverified.svg'),
                            kwidth5,
                            Expanded(
                              flex: 4,
                              child: Text(
                                addressList.length > 0
                                    ? '${addressList.first.street} ${addressList.first.thoroughfare}  ${addressList.first.subLocality} ${addressList.first.locality} ${addressList.first.postalCode}'
                                    : '',
                                maxLines: 2,
                                softWrap: true,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 15,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Center(
                                child: ButtonAnimation(
                                  onpress: (() {
                                    Get.off(() => MapPage());
                                  }),
                                  animationWidget: Container(
                                    decoration: BoxDecoration(
                                        border: Border.all(color: kblue),
                                        borderRadius: BorderRadius.circular(4)),
                                    child: const Text(
                                      'Change',
                                      style: TextStyle(color: kblue),
                                    ).paddingAll(
                                      8,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Spacer(),
                      loading
                          ? CustomAppShimmer(
                              child: Container(
                              color: Colors.grey,
                              height: 56,
                              width: double.infinity,
                            ))
                          : SubmitButton(
                              text: outofService == true
                                  ? 'Out of Service'
                                  : 'Confirm Location',
                              onpress: outofService == true
                                  ? null
                                  : () {
                                      checkServicableFn(
                                          addressList.first.postalCode!,
                                          widget.page);
                                    }),
                    ],
                  ),
                  // width: MediaQuery.of(context).size.width / 2,
                ),
              ],
            ),
          ),
        ],
      ),
      // floatingActionButton: FloatingActionButton.extended(
      //   isExtended: true,
      //   icon: Icon(Icons.location_on),
      //   label: Text('Continue'),
      //   onPressed: () {},
      // ),
    );
  }

  checkServicableFn(String pincode, page) async {
    print('currentPosition $currentPosition');

    if (pincode.length < 6) {
      print('invalid pincode $pincode');
      // showale
      // showAlertDialog(context, 'Required', 'Invalid pincode.');
      // return false;
    }
    GetStorage storage = GetStorage();
    storage.write('tempPincode', pincode);
    int userType = storage.read('userType') ?? 0;
    print('userType $userType');

    var pincodeReq = {
      'pincode': pincode,
      // 'lat': currentPosition.latitude,
      // 'long': currentPosition.longitude,
    };
    var pincodeReqWToken = {
      'pincode': pincode,
      // 'lat': currentPosition.latitude,
      // 'long': currentPosition.longitude,
      "key": "JWT",
      "secret": "RAPSAP",
    };

    var dd = await UserService.checkServiceable(
      userType == 0 ? pincodeReqWToken : pincodeReq,
    );
    print('pincode result $dd');
    if (dd['success'] == true) {
      storage.write('storeID', dd['data']['store_id']);
      storage.write('place', dd['data']['description']);

      _userController.locationaddress.value = addressList.first;
      storage.write(
          'locationaddress', _userController.locationaddress.value.toJson());
      _userController.locationpoint.value = {
        'latitude': currentPosition.latitude,
        'longitude': currentPosition.longitude
      };
      storage.write('locationpoint', _userController.locationpoint.value);
      if (storage.read('loginstatus') == null) {
        final HomeViewController controller = Get.find();
        controller.getHomePageViews();

        DatabaseHelper.instance.getGroceries();
        Get.to(() => RootPage());
        return;
      }

      if (userType == 0) {
        if (page == Pagestep.addaddress) {
          Get.back();
        } else if (accountController.addressModel.value.data == null) {
          Get.to(() => AddressAddEditPage(mode: addEditAddress.add));
        } else {
          final HomeViewController controller = Get.find();
          controller.getHomePageViews();

          DatabaseHelper.instance.getGroceries();
          Get.offAll(() => RootPage());
        }
      }
      // else {
      //   // Get.to(() => AddressPage(step: 1));
      // }
    } else {
      // showAlertDialog(context, "Pincode is not servicable!",
      //     "We are increasing our reach everyday. We shall service your locality soon");
    }
  }
}

showAlertDialog(BuildContext context, String title, String content) {
  // set up the button
  Widget okButton = SubmitButton(
    height: 45,
    text: "OK",
    onpress: () {
      Get.close(1);
    },
  ).paddingSymmetric(horizontal: 30, vertical: 8);

  // set up the AlertDialog
  AlertDialog alert = AlertDialog(
    title: Text(title),
    content: Text(content),
    actions: [
      okButton,
    ],
  );

  // show the dialog
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return alert;
    },
  );
}

class CustomAppShimmer extends StatelessWidget {
  final Widget child;

  const CustomAppShimmer({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: ClipRRect(borderRadius: BorderRadius.circular(2), child: child));
  }
}
