import 'dart:io';

import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:rapsap/controllers/cartcontrolller.dart';
import 'package:rapsap/controllers/home_view_controller.dart';
import 'package:rapsap/services/userservices.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:rapsap/view/widgets/notification_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/services.dart';
import 'package:rapsap/controllers/accountscontroller.dart';
import 'package:rapsap/controllers/root_view_controller.dart';
import 'package:rapsap/view/screens/Home_screen/home_screen.dart';
import 'package:rapsap/view/screens/account_screen/account_screen.dart';
import 'package:rapsap/view/screens/cart_screen/cart_screen.dart';
import 'package:upgrader/upgrader.dart';

import '../../../services/accountservices.dart';
import '../../../services/firebaseservices.dart';
import '../../widgets/Internetconnectiviity.dart';
import '../account_screen/deleteaccount.dart';

class RootPage extends StatefulWidget {
  const RootPage({
    super.key,
    this.data,
  });
  final bool? data;

  @override
  State<RootPage> createState() => _RootPageState();
}

class _RootPageState extends State<RootPage> with WidgetsBindingObserver {
  final internetConnectivity = InternetConnectivity();

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    internetConnectivity.initConnectivity();
    internetConnectivity.startListening();
    showdialog();
    checkdailyrewards();

    setState(() {});
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      UserService.getUserConfig("home");

      // if (userController.tester.value != true) {
      //   Get.offAll(() => const LaunchingSoon());
      //   return;
      // }
      if (GetStorage('payload').read('payload') != null &&
          GetStorage('payload').read('payload') != "") {
        handlenavigation(payload: GetStorage('payload').read('payload'))
            .then((value) => GetStorage('payload').erase());
      }

      final link = await FirebaseDynamicLinks.instance.getInitialLink();
      if (link != null) {
        dynamiclinknavigation(payload: link.link.toString());
      }

      FirebaseService.dynamicLinks!.onLink.listen((dynamicLinkData) {
        String uri = dynamicLinkData.link.toString();
        dynamiclinknavigation(payload: uri);
      });
    });

    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    // InternetConnectivity().connectivitySubscription.cancel();

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      InternetConnectivity().initConnectivity();
    }
  }

  void listenInternetChanges(ConnectivityResult event) {
    if (mounted) {
      setState(() {});
    }

    InternetConnectivity().updateConnectionStatus(event).then((value) {
      showdialog();
      checkdailyrewards();
    });
  }

  checkdailyrewards() {
    final controller = Get.find<HomeViewController>();
    final userController = Get.find<UserController>();
    if (userController.userdata.value.data != null) {
      AccountService.checkDailyRewards().then((value) {
        print('daily rewards value $value');
        if (value['success'] == true) {
          controller.rewards.value = true;
        } else {
          controller.rewards.value = false;
        }
      });
    }
  }

  showdialog() async {
    // if (widget.data == true) {
    //   Future.delayed(Duration(milliseconds: 200), (() async {
    //     // await deleteddialog(context);

    //     // setState(() {});
    //   }));
    // }

    // final UserController userController = Get.find();

    if (widget.data == true) {
      Future.delayed(const Duration(milliseconds: 200), (() async {
        await deletestatusdialog(context);

        // setState(() {});
      }));
    }
  }

  final controller = Get.put(RootViewController());

  final AccountController accountController =
      Get.put<AccountController>(AccountController());

  final CartController cartController =
      Get.put<CartController>(CartController());

  @override
  Widget build(BuildContext context) {
    // deletestatusdialog(context);

    List<Widget> screens = <Widget>[
      HomeScreen(),
      // OfferScreen(),
      const CartScreen(
        type: 'root',
      ),
      AccountScreen(),
    ];
    return WillPopScope(
      onWillPop: () async {
        if (controller.selectedIndex == 0) {
          SystemNavigator.pop(animated: true);
        } else {
          controller.selectedIndex = 0;
        }
        return false;
      },
      child: Scaffold(
        backgroundColor: kwhite,
        body: UpgradeAlert(
          child: GetBuilder<RootViewController>(builder: (controller) {
            return screens.elementAt(controller.selectedIndex);
          }),
        ),
        bottomNavigationBar: GetBuilder<HomeViewController>(
          id: "home",
          builder: (controller) {
            return controller.categoryModel.isEmpty ||
                    controller.categoryModel.first == null ||
                    controller.bannerItems.isEmpty
                ? const SizedBox()
                : GetBuilder<RootViewController>(
                    builder: (controller) {
                      return SizedBox(
                          height: Platform.isIOS ? 120 : 100,
                          child: _bottomNavBar(controller));
                    },
                  );
          },
        ),
      ),
    );
  }

  BottomNavigationBar _bottomNavBar(RootViewController controller) =>
      BottomNavigationBar(
        enableFeedback: true,
        type: BottomNavigationBarType.fixed,
        elevation: 5,
        backgroundColor: Colors.white,
        items: <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Column(
              children: [
                Container(
                  constraints:
                      const BoxConstraints(minHeight: 25, maxHeight: 30),
                  child: getSvgIcon(
                    "assets/svg/home-icon.svg",
                    controller.selectedIndex == 0
                        ? const Color(0xff5074F3)
                        : const Color(0xffcacaca),
                  ),
                ),
                kheight10,
                controller.selectedIndex == 0
                    ? Container(
                        height: 3,
                        color: kblue,
                        width: double.infinity,
                      ).paddingSymmetric(horizontal: 40)
                    : const SizedBox()
              ],
            ),
            label: '',
          ),
          // BottomNavigationBarItem(
          //   icon: getSvgIcon(
          //     "assets/svg/offers-icon.svg",
          //     controller.selectedIndex == 1
          //         ? const Color(0xff5074F3)
          //         : Colors.black,
          //   ),
          //   label: 'Offers',
          // ),
          BottomNavigationBarItem(
            icon: Column(
              children: [
                Stack(
                  children: [
                    Container(
                      constraints:
                          const BoxConstraints(minHeight: 25, maxHeight: 30),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: getSvgIcon(
                          "assets/svg/cart-bag.svg",
                          controller.selectedIndex == 1
                              ? const Color(0xff5074F3)
                              : const Color(0xffCACACA),
                        ),
                      ),
                    ),
                    Obx(() => cartController.myCartItems.value.isNotEmpty
                        ? Positioned(
                            right: 0,
                            top: 0,
                            child: Bounceable(
                              onTap: () {
                                
                              },
                              scaleFactor: 0.96,
                            duration: const Duration(milliseconds: 550),
                              child: CircleAvatar(
                                backgroundColor: Colors.red.shade300,
                                radius: 8,
                                child: Text(
                                  cartController.myCartItems.value
                                      .fold<int>(
                                          0,
                                          (previousValue, element) =>
                                              previousValue + element.qty!)
                                      .toString(),
                                  style: const TextStyle(
                                      color: kwhite,
                                      fontSize: 8,
                                      fontWeight: FontWeight.w700),
                                ),
                              ),
                            ),
                          )
                        : const SizedBox()),
                  ],
                ),
                kheight10,
                controller.selectedIndex == 1
                    ? Container(
                        height: 3,
                        color: kblue,
                        width: double.infinity,
                      ).paddingSymmetric(horizontal: 40)
                    : const SizedBox()
              ],
            ),
            label: '',
          ),
          BottomNavigationBarItem(
            icon: Column(
              children: [
                Container(
                  constraints:
                      const BoxConstraints(minHeight: 25, maxHeight: 30),
                  child: getSvgIcon(
                    "assets/svg/account-icon.svg",
                    controller.selectedIndex == 2
                        ? const Color(0xff5074F3)
                        : const Color(0xffCACACA),
                  ),
                ),
                kheight10,
                controller.selectedIndex == 2
                    ? Container(
                        height: 3,
                        color: kblue,
                        width: double.infinity,
                      ).paddingSymmetric(horizontal: 40)
                    : const SizedBox()
              ],
            ),
            label: '',
          ),
        ],
        currentIndex: controller.selectedIndex,
        selectedItemColor: const Color(0xff5074F3),
        unselectedItemColor: Colors.black,
        selectedLabelStyle: GoogleFonts.inter(fontSize: 12),
        unselectedLabelStyle: GoogleFonts.inter(fontSize: 12),
        onTap: controller.onScreenSelected,
      );

  Widget getSvgIcon(String icon, Color color) => SvgPicture.asset(
        icon,
        color: color,
        semanticsLabel: 'Svg',
      );
}
