import 'package:package_info_plus/package_info_plus.dart';
import 'package:rapsap/view/screens/mapscreen/searchpage.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:rapsap/view/widgets/custom.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutPage extends StatefulWidget {
  const AboutPage({Key? key}) : super(key: key);

  @override
  State<AboutPage> createState() => _AboutPageState();
}

class _AboutPageState extends State<AboutPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kwhite,
      appBar: AppBar(
        toolbarHeight: 0,
        iconTheme: const IconThemeData(color: kblue),
        backgroundColor: Colors.white,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: Column(
            children: [
              Row(
                children: [
                  IconButton(
                      onPressed: () {
                        Get.back();
                      },
                      icon: const Icon(
                        Icons.arrow_back_sharp,
                        color: kblue,
                      )),
                  kwidth30,
                  const Text(
                    'About',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 20,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const CommonDivider()
            ],
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                ListTile(
                  onTap: () async {
                    if (!await launchUrl(
                        Uri.parse('https://rapsap.com/faq.html'))) {
                      throw "Could not launch https://rapsap.com/faq.html";
                    }
                  },
                  title: const Text('FAQ'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                ),
                const Divider(),
                ListTile(
                    onTap: () async {
                      if (!await launchUrl(
                          Uri.parse('https://rapsap.com/terms.html'))) {
                        throw "Could not launch https://rapsap.com/terms.html";
                      }
                    },
                    title: const Text('Terms & Conditions'),
                    trailing: const Icon(Icons.arrow_forward_ios)),
                const Divider(),
                ListTile(
                    onTap: () async {
                      if (!await launchUrl(
                          Uri.parse('https://rapsap.com/privacy.html'))) {
                        throw "Could not launch https://rapsap.com/privacy.html";
                      }
                    },
                    title: const Text('Privacy Policy'),
                    trailing: const Icon(Icons.arrow_forward_ios)),
              ],
            ),
            hgap(context.height * 0.5),
            FutureBuilder<PackageInfo>(
              future: PackageInfo.fromPlatform(),
              builder: (context, snapshot) {
                switch (snapshot.connectionState) {
                  case ConnectionState.done:
                    return Align(
                      alignment: Alignment.bottomCenter,
                      child: Text(
                        'Version ${snapshot.data!.version} + ${snapshot.data!.buildNumber}',
                        style: TextStyle(
                            color: kblack.withOpacity(0.5),
                            fontWeight: FontWeight.w500),
                      ),
                    );
                  default:
                    return const SizedBox();
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
