import 'package:flutter/material.dart';

import '../../../../utils/constants.dart';

class AddForm<PERSON>ield extends StatelessWidget {
  const AddFormField(
      {Key? key,
      required this.fieldLabel,
      required this.fieldController,
      required this.hintText})
      : super(key: key);

  final String fieldLabel;
  final TextEditingController fieldController;
  final String hintText;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          fieldLabel,
          style: headStyleNormal.copyWith(
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
        ),
        kheight10,
        TextField(
            enabled: fieldLabel == "Mobile" && fieldController.text.length >= 10
                ? false
                : true,
            readOnly:
                fieldLabel == "Mobile" && fieldController.text.length >= 10
                    ? true
                    : false,
            controller: fieldController,
            style: headSty<PERSON><PERSON><PERSON><PERSON>ield,
            keyboardType: TextInputType.text,
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: headStyleNormal.copyWith(
                fontSize: 16,
                color: const Color(0xFFC2C2C2),
                fontWeight: FontWeight.normal,
              ),
              errorBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: kblack.withOpacity(0.3),
                ),
                borderRadius: BorderRadius.circular(0),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.black54, width: 1),
                borderRadius: BorderRadius.circular(0),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.black54, width: 1),
                borderRadius: BorderRadius.circular(0),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(0),
                borderSide: const BorderSide(
                  width: 1,
                  style: BorderStyle.none,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(0),
                borderSide: const BorderSide(
                  width: 0.5,
                ),
              ),

              contentPadding:
                  const EdgeInsets.only(top: 10, left: 10, bottom: 10),
              counterStyle: const TextStyle(
                height: double.minPositive,
              ),
              counterText: "",

              // border: OutlineInputBorder(
              //   borderSide: BorderSide(color: Colors.white),
              // ),
              // focusedBorder: OutlineInputBorder(
              //   borderSide: BorderSide(color: Colors.white),
              // ),

              fillColor:
                  fieldLabel == "Mobile" && fieldController.text.length >= 10
                      ? kgrey.withOpacity(0.4)
                      : const Color(0xFFFFFFFF),
              filled: true,
            )),
      ],
    );
  }
}
