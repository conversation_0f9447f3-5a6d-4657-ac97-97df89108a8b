import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:rapsap/controllers/accountscontroller.dart';
import 'package:rapsap/controllers/ordercontroller.dart';
import 'package:rapsap/view/screens/AddressPage/myaddress.dart';
import 'package:rapsap/view/screens/account_screen/editprofile.dart';
import 'package:rapsap/view/screens/wishlist/wishlistscreen.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:rapsap/view/screens/ordersection/myorders.dart';
import 'package:url_launcher/url_launcher.dart';

import '../rewards/rewardlistscreen.dart';
import 'aboutpage.dart';

class AccountScreen extends StatelessWidget {
  AccountScreen({Key? key}) : super(key: key);
  final UserController userController = Get.find<UserController>();
  final OrderController orderController = Get.find<OrderController>();

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print(userController.userdata.value.data);
    }

    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: Platform.isIOS
            ? SystemUiOverlayStyle.dark
            : SystemUiOverlayStyle.light,
        toolbarHeight: 60,
        titleSpacing: 30,
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: kblack,
        centerTitle: false,
        automaticallyImplyLeading: false,
        title: const Text(
          'My Accounts',
          style: TextStyle(color: kblack, fontWeight: FontWeight.w600),
        ),
      ),
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CircleAvatar(
                      radius: 28,
                      backgroundColor: kblack,
                      child: userController
                                  .userdata.value.data?.userProfileImage !=
                              null
                          ? ClipOval(
                              child: Image.network(userController
                                  .userdata.value.data!.userProfileImage),
                            )
                          : const Icon(
                              Icons.person,
                            )),
                  kwidth5,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                  userController.userdata.value.data == null
                                      ? "Guest User"
                                      : "${userController.userdata.value.data!.firstName}",
                                  style: const TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                      overflow: TextOverflow.ellipsis)),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                userController.userdata.value.data == null
                                    ? ""
                                    : userController
                                            .userdata.value.data!.email ??
                                        "",
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                        border: Border.all(color: kblue),
                        borderRadius: BorderRadius.circular(4)),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: userController.userdata.value.data == null
                          ? InkWell(
                              onTap: () {
                                Get.to(() => const LoginScreen(),
                                    fullscreenDialog: true);
                              },
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.login_outlined,
                                    size: 15,
                                    color: kblue,
                                  ),
                                  const Text(
                                    'Login',
                                    style: TextStyle(color: kblue),
                                  ).paddingAll(
                                    8,
                                  ),
                                ],
                              ),
                            )
                          : InkWell(
                              onTap: (() {
                                Get.to(() => EditProfileScreen());
                              }),
                              child: Row(
                                children: [
                                  const Icon(
                                    CupertinoIcons.pencil,
                                    size: 15,
                                    color: kblue,
                                  ),
                                  const Text(
                                    'Edit',
                                    style: TextStyle(color: kblue),
                                  ).paddingAll(
                                    8,
                                  ),
                                ],
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
            Divider(
              height: 2,
              color: kblack.withOpacity(0.06),
              thickness: 3,
            ),
            kheight10,
            AccountPageListTile(
                title: 'My Orders',
                leading: SvgPicture.asset('assets/svg/orders_icon.svg'),
                ontap: () {
                  if (userController.userdata.value.data == null) {
                    Get.to(() => const LoginScreen(), fullscreenDialog: true);
                  } else {
                    orderController.getOrders();

                    Get.to(() => MyOrders());
                  }
                }),
            // Divider(
            //   height: 0,
            //   color: kblack.withOpacity(0.06),
            //   thickness: 1,
            // ),
            // AccountPageListTile(
            //     title: 'My Subscriptions',
            //     leading: SvgPicture.asset('assets/svg/subscribtion.svg'),
            //     ontap: () {
            //       Get.to(() => SubscriptionCommingSoon());
            //     }),
            Divider(
              height: 0,
              color: kblack.withOpacity(0.06),
              thickness: 1,
            ),
            AccountPageListTile(
                title: 'My Rewards',
                leading: SvgPicture.asset(
                  'assets/svg/gift.svg',
                  color: kblack,
                ),
                ontap: () {
                  userController.userdata.value.data == null
                      ? Get.to(() => const LoginScreen(),
                          fullscreenDialog: true)
                      : Get.to(() => MyRewardsScreen());
                }),
            Divider(
              height: 0,
              color: kblack.withOpacity(0.06),
              thickness: 1,
            ),
            AccountPageListTile(
                title: 'My Wishlist',
                leading: SvgPicture.asset('assets/svg/wishlist.svg'),
                ontap: () {
                  userController.userdata.value.data == null
                      ? Get.to(() => const LoginScreen(),
                          fullscreenDialog: true)
                      : Get.to(() => WishListScreen());
                }),
            Divider(
              height: 0,
              color: kblack.withOpacity(0.06),
              thickness: 1,
            ),
            AccountPageListTile(
                title: 'My Address',
                leading: SvgPicture.asset('assets/svg/addressicon.svg'),
                ontap: () {
                  userController.userdata.value.data == null
                      ? Get.to(() => const LoginScreen(),
                          fullscreenDialog: true)
                      : Get.to(() => MyAddressScreen());
                }),
            Divider(
              height: 0,
              color: kblack.withOpacity(0.06),
              thickness: 1,
            ),

            // AccountPageListTile(
            //     title: 'Payment Methods',
            //     leading: SvgPicture.asset("assets/svg/payment.svg"),
            //     ontap: () {}),
            // Divider(
            //   height: 0,
            //   color: kblack.withOpacity(0.06),
            //   thickness: 3,
            // ),
            AccountPageListTile(
                title: 'Customer Support',
                leading: SvgPicture.asset('assets/svg/customer_support.svg'),
                ontap: () async {
                  if (!await launchUrl(Uri.parse(url()))) {
                    throw "Could not launch whatsapp";
                  }
                }),
            Divider(
              height: 0,
              color: kblack.withOpacity(0.06),
              thickness: 1,
            ),
            AccountPageListTile(
                title: 'About',
                leading: SvgPicture.asset('assets/svg/about_icon.svg'),
                ontap: () {
                  Get.to(const AboutPage());
                }),
            Divider(
              height: 0,
              color: kblack.withOpacity(0.06),
              thickness: 1,
            ),
            userController.userdata.value.data != null
                ? AccountPageListTile(
                    title: 'Logout',
                    leading: const Icon(
                      Icons.logout_sharp,
                      color: kblack,
                    ),
                    ontap: () async {
                      await showAlertDialog(context, 'Logout',
                          'Confirm Logout from this account');
                    })
                : const SizedBox(),
            Divider(
              height: 0,
              color: kblack.withOpacity(0.06),
              thickness: 1,
            ),
            kheight20,

            FutureBuilder<PackageInfo>(
              future: PackageInfo.fromPlatform(),
              builder: (context, snapshot) {
                switch (snapshot.connectionState) {
                  case ConnectionState.done:
                    return Align(
                      alignment: Alignment.bottomCenter,
                      child: Text(
                        'Version ${snapshot.data!.version} + ${snapshot.data!.buildNumber}',
                        style: TextStyle(
                            color: kblack.withOpacity(0.5),
                            fontWeight: FontWeight.w500),
                      ),
                    );
                  default:
                    return const SizedBox();
                }
              },
            ),
            kheight20,
          ],
        ),
      ),
    );
  }

  String url() {
    if (Platform.isIOS) {
      return "whatsapp://wa.me/************/";
    } else {
      return "whatsapp://send?phone=************";
    }
  }

  showAlertDialog(BuildContext context, String title, String content) {
    Widget okButton = TextButton(
      child: const Text("OK"),
      onPressed: () async {
        final accountController = Get.find<AccountController>();
        accountController.logoutUser();
      },
    );
    Widget cancelButton = TextButton(
      child: const Text("Cancel"),
      onPressed: () {
        Get.close(1);
      },
    );
    Widget alert = Platform.isAndroid
        ? AlertDialog(
            // "Pincode is not Servicable!"
            // "We are increasing our reach everyday. We shall service your locality soon"
            title: Text(title),
            content: Text(content),
            actions: [okButton, cancelButton],
          )
        : CupertinoAlertDialog(
            // "Pincode is not Servicable!"
            // "We are increasing our reach everyday. We shall service your locality soon"
            title: Text(title),
            content: Text(content),
            actions: [okButton, cancelButton],
          );

    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }
}

class AccountPageListTile extends StatelessWidget {
  final title;

  final ontap;

  final leading;

  const AccountPageListTile({
    Key? key,
    this.title,
    this.ontap,
    this.leading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 60,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: ListTile(
          minLeadingWidth: 30,
          title: Text(title,
              style: const TextStyle(
                color: kblack,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              )),
          leading: leading,
          onTap: ontap,
          trailing: const Icon(
            Icons.arrow_forward_ios,
            size: 18,
            color: kblack,
          ),
        ),
      ),
    );
  }
}
