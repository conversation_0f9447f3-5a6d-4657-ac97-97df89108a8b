import 'package:flutter/material.dart';

import 'package:rapsap/utils/constants.dart';

class LoadingScreen extends StatelessWidget {
  const LoadingScreen({Key? key, required this.text}) : super(key: key);
  final String text;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
          child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator.adaptive(
            strokeWidth: 5,
            backgroundColor: kgrey,
            valueColor: AlwaysStoppedAnimation<Color>(kblack),
          ),
          kheight20,
          Text(
            text,
            style: const TextStyle(fontWeight: FontWeight.bold),
          )
        ],
      )),
    );
  }
}
