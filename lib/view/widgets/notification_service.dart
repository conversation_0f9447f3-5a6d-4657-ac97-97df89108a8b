import 'dart:developer';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:rapsap/firebase_options.dart';
import 'package:rapsap/main.dart';
import 'package:rapsap/view/screens/account_screen/editprofile.dart';
import 'package:rapsap/view/screens/cart_screen/cart_screen.dart';
import 'package:rapsap/view/screens/category_pages/category_page.dart';
import 'package:rapsap/view/screens/ordersection/orderdetail.dart';
import 'package:rapsap/view/screens/product_screen/product_screen.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

final FlutterLocalNotificationsPlugin? flutterLocalNotificationsPlugin =
    kIsWeb ? null : FlutterLocalNotificationsPlugin();

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  if (!kIsWeb) {
    try {
      // Enhanced Firebase initialization check for background handler
      if (Firebase.apps.isEmpty) {
        // Add delay to ensure platform is ready
        await Future.delayed(const Duration(milliseconds: 300));
        
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
        
        log('Firebase initialized in background handler');
      }
    } catch (e) {
      log('Firebase initialization in background handler: $e');
      // Continue processing notification even if Firebase init fails
    }
    
    try {
      NotificationService().triggerNotification(message);
    } catch (e) {
      log('Error triggering background notification: $e');
    }
  }
}

@pragma('vm:entry-point')
notificationData(dynamic value) async {
  if (!kIsWeb && value.payload != null) {
    try {
      handlenavigation(payload: value.payload!);
    } catch (e) {
      log('Error handling notification data: $e');
    }
  }
}

class NotificationService {
  static bool _isInitialized = false;
  static bool _initializationInProgress = false;
  
  Future<void> setupInteractedMessage() async {
    if (kIsWeb || !_isInitialized) return;

    try {
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        // Add delay to ensure Firebase is fully ready
        await Future.delayed(const Duration(milliseconds: 200));
        
        RemoteMessage? initialMessage =
            await FirebaseMessaging.instance.getInitialMessage();

        if (initialMessage != null) {
          _handleMessage(initialMessage);
        }
        FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
      }
    } catch (e) {
      log('Error setting up interacted message: $e');
    }
  }

  void _handleMessage(RemoteMessage message) {
    try {
      String payload = getPayload(message.data);
      GetStorage('payload').write('payload', payload.toString());
    } catch (e) {
      log('Error handling message: $e');
    }
  }

  Future<void> init() async {
    // Skip initialization on web
    if (kIsWeb) return;
    
    if (_isInitialized) {
      log('NotificationService already initialized');
      return;
    }

    if (_initializationInProgress) {
      log('NotificationService initialization in progress, waiting...');
      while (_initializationInProgress && !_isInitialized) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return;
    }

    _initializationInProgress = true;

    try {
      // Enhanced Firebase readiness check
      if (Firebase.apps.isEmpty) {
        log('Firebase not initialized, skipping notification setup');
        _initializationInProgress = false;
        return;
      }

      // Add delay to ensure Firebase services are fully ready
      await Future.delayed(const Duration(milliseconds: 300));

      // Initialize Firebase Messaging listeners with enhanced error handling
      await _initializeMessagingListeners();

      // Initialize local notifications
      await _initializeLocalNotifications();
      
      _isInitialized = true;
      _initializationInProgress = false;
      log('Notification service initialized successfully');
      
    } catch (e) {
      log('Error initializing notification service: $e');
      _isInitialized = false;
      _initializationInProgress = false;
    }
  }

  /// Initialize Firebase Messaging listeners with better error handling
  Future<void> _initializeMessagingListeners() async {
    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
        FirebaseMessaging.onMessageOpenedApp.listen(getMessages);
        FirebaseMessaging.onMessage.listen(getMessages);
        
        log('Android FCM listeners set up');
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
        FirebaseMessaging.onMessageOpenedApp.listen(onMessageOpenedAppFn);
        
        // Set foreground notification options for iOS
        await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
          alert: true, 
          badge: false, 
          sound: true
        );
        
        log('iOS FCM listeners set up');
      }
    } catch (e) {
      log('Error setting up messaging listeners: $e');
      rethrow;
    }
  }

  /// Initialize local notifications with enhanced error handling
  Future<void> _initializeLocalNotifications() async {
    final notificationPlugin = flutterLocalNotificationsPlugin;
    if (notificationPlugin == null) return;

    try {
      await notificationPlugin.getNotificationAppLaunchDetails();

      // Platform-specific initialization
      if (defaultTargetPlatform == TargetPlatform.android) {
        await _initializeAndroidNotifications(notificationPlugin);
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        await _initializeIOSNotifications(notificationPlugin);
      }

      // Initialize the notification plugin
      await _initializeNotificationPlugin(notificationPlugin);

      // Handle app launch from notification
      await _handleAppLaunchFromNotification(notificationPlugin);

    } catch (e) {
      log('Error initializing local notifications: $e');
      rethrow;
    }
  }

  /// Initialize Android-specific notification settings
  Future<void> _initializeAndroidNotifications(FlutterLocalNotificationsPlugin plugin) async {
    try {
      await plugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
      
      log('Android notification channel created');
    } catch (e) {
      log('Error creating Android notification channel: $e');
    }
  }

  /// Initialize iOS-specific notification settings
  Future<void> _initializeIOSNotifications(FlutterLocalNotificationsPlugin plugin) async {
    try {
      await plugin
          .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(alert: true, badge: true, sound: true);
      
      log('iOS notification permissions requested');
    } catch (e) {
      log('Error requesting iOS notification permissions: $e');
    }
  }

  /// Initialize notification plugin with settings
  Future<void> _initializeNotificationPlugin(FlutterLocalNotificationsPlugin plugin) async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    final DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestSoundPermission: false,
      requestBadgePermission: false,
      requestAlertPermission: false,
      defaultPresentAlert: true,
      defaultPresentSound: true,
      notificationCategories: [
        DarwinNotificationCategory(
          'category',
          options: {
            DarwinNotificationCategoryOption.allowAnnouncement,
          },
          actions: [
            DarwinNotificationAction.plain('snoozeAction', 'snooze'),
            DarwinNotificationAction.plain(
              'confirmAction',
              'confirm',
              options: {
                DarwinNotificationActionOption.authenticationRequired,
              },
            ),
          ],
        ),
      ],
    );

    final InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS
    );

    await plugin.initialize(
      initializationSettings,
      onDidReceiveBackgroundNotificationResponse: notificationData,
      onDidReceiveNotificationResponse: (dynamic value) async {
        log('payload ${value.payload.toString()}');
        if (value.payload != null) {
          try {
            handlenavigation(payload: value.payload!);
          } catch (e) {
            log('Error handling notification response: $e');
          }
        }
      },
    );
  }

  /// Handle app launch from notification
  Future<void> _handleAppLaunchFromNotification(FlutterLocalNotificationsPlugin plugin) async {
    try {
      final notificationOnLaunchDetails = await plugin.getNotificationAppLaunchDetails();

      log("notificationOnLaunchDetails ${notificationOnLaunchDetails.toString()}");

      if (notificationOnLaunchDetails?.didNotificationLaunchApp ?? false) {
        log("didNotificationLaunchApp ${notificationOnLaunchDetails?.didNotificationLaunchApp}");
        if (notificationOnLaunchDetails!.notificationResponse!.payload != null) {
          GetStorage('payload').write(
              'payload',
              notificationOnLaunchDetails.notificationResponse!.payload.toString());
        }
      }
    } catch (e) {
      log('Error handling app launch from notification: $e');
    }
  }

  AndroidNotificationChannel channel = const AndroidNotificationChannel(
    'high_importance_channel', // id
    'High Importance Notifications', // title
    importance: Importance.high,
    playSound: true,
  );

  void onMessageOpenedAppFn(RemoteMessage message) {
    log("onMessageOpenedAppFn");
    try {
      String payload = getPayload(message.data);
      handlenavigation(payload: payload).then((value) {
        GetStorage('payload').write('payload', "");
      });
    } catch (e) {
      log('Error in onMessageOpenedAppFn: $e');
    }
  }

  void getMessages(RemoteMessage message) {
    log('get message fun called');
    if (kDebugMode) {
      log('Got a message whilst in the foreground!');
    }
    triggerNotification(message);
  }

  void triggerNotification(RemoteMessage message) {
    if (kIsWeb) return;

    try {
      final notificationPlugin = flutterLocalNotificationsPlugin;
      if (notificationPlugin == null) return;

      RemoteNotification? notification = message.notification;
      AndroidNotification? android = message.notification?.android;

      if (android != null && notification != null) {
        log('android:: ${notification.android}');
        log('apple:: ${notification.apple}');

        notificationPlugin.show(
          message.messageId.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
            android: AndroidNotificationDetails(
              channel.id,
              channel.name,
              playSound: true,
              importance: Importance.high,
              icon: '@mipmap/ic_launcher',
              fullScreenIntent: true,
              channelShowBadge: true,
            ),
            iOS: const DarwinNotificationDetails(
                interruptionLevel: InterruptionLevel.active,
                presentSound: true,
                presentAlert: true),
          ),
          payload: getPayload(message.data),
        );
      } else {
        log("custom notification");

        notificationPlugin.show(
          message.messageId.hashCode,
          message.data['title'] ?? 'Notification',
          message.data['body'] ?? '',
          NotificationDetails(
              android: AndroidNotificationDetails(
                channel.id,
                channel.name,
                playSound: true,
                importance: Importance.high,
                icon: '@mipmap/ic_launcher',
                fullScreenIntent: true,
                channelShowBadge: true,
              ),
              iOS: const DarwinNotificationDetails(
                  interruptionLevel: InterruptionLevel.active,
                  presentSound: true,
                  presentAlert: true)),
          payload: getPayload(message.data),
        );
      }
    } catch (e) {
      log('Error triggering notification: $e');
    }
  }

  String getPayload(Map<String, dynamic> data) {
    try {
      if (data['type'] == "order") {
        log("order payload called");
        return "${data['type']}+${data['order_id']}";
      } else if (data['type'] == "category") {
        return "${data['type']}+${data['category_id']}";
      } else if (data['type'] == "subcategory") {
        return "${data['type']}+${data['subcategory_id']}+${data['category_id']}";
      } else if (data['type'] == "product") {
        return "${data['type']}+${data['product_id']}";
      } else {
        return data['type'] ?? '';
      }
    } catch (e) {
      log('Error getting payload: $e');
      return '';
    }
  }
}

Future<void> handlenavigation({required String payload}) async {
  try {
    GetStorage('payload').erase();
    log("payload my $payload");

    if (payload.split('+').first == "order") {
      if (storage.read('userdata') != null) {
        return Get.to(() => OrderDetailScreen(
              orderId: payload.split('+').last,
            ));
      }
    }
    if (payload.split('+').first == "category") {
      return Get.to(() => CategoryScreen(
            categoryid: int.parse(payload.split('+').last),
            categoryname: '',
          ));
    }
    if (payload.split('+').first == "subcategory") {
      return Get.to(
        () => CategoryScreen(
          categoryid: int.parse(payload.split('+').last),
          categoryname: '',
          subcategoryid: int.parse(
            payload.split('+').elementAt(1),
          ),
        ),
      );
    }

    if (payload.split('+').first == "product") {
      log("productid=${payload.split('+').last}");
      return Get.to(() => Scaffold(
            appBar: AppBar(
              elevation: 0,
              foregroundColor: kblack,
              backgroundColor: kwhite,
            ),
            body: ProductScreen(productId: int.parse(payload.split('+').last)),
          ));
    }
    if (payload == "profile") {
      if (storage.read('userdata') != null) {
        return Get.to(() => EditProfileScreen());
      }
    }
    if (payload == "cart") {
      return Get.to(() => const CartScreen());
    }
  } catch (e) {
    log('Error handling navigation: $e');
  }
}

Future<void> dynamiclinknavigation({required String payload}) async {
  try {
    log("payload my $payload");

    if (payload.split('/').last == "order") {
      if (storage.read('userdata') != null) {
        return Get.to(() => OrderDetailScreen(
              orderId: payload.split('/')[payload.split('/').length - 2],
            ));
      }
    }

    if (payload.split('/').first == "category") {
      return Get.to(() => CategoryScreen(
            categoryid:
                int.parse(payload.split('/')[payload.split('/').length - 2]),
            categoryname: '',
          ));
    }
    if (payload.split('/').first == "subcategory") {
      return Get.to(
        () => CategoryScreen(
          categoryid:
              int.parse(payload.split('/')[payload.split('/').length - 2]),
          categoryname: '',
          subcategoryid: int.parse(
            payload.split('/')[payload.split('/').length - 3],
          ),
        ),
      );
    }

    if (payload.split('/').last == "product") {
      return Get.to(() => Scaffold(
            appBar: AppBar(
              elevation: 0,
              foregroundColor: kblack,
              backgroundColor: kwhite,
            ),
            body: ProductScreen(
                productId:
                    int.parse(payload.split('/')[payload.split('/').length - 2])),
          ));
    }
    if (payload.split('/').last == "profile") {
      if (storage.read('userdata') != null) {
        return Get.to(() => EditProfileScreen());
      }
    }
    if (payload.split('/').last == "me") {
      if (storage.read('userdata') != null) {
        return Get.to(() => EditProfileScreen());
      }
    }
    if (payload.split('/').last == "cart") {
      return Get.to(() => const CartScreen());
    }
  } catch (e) {
    log('Error handling dynamic link navigation: $e');
  }
}