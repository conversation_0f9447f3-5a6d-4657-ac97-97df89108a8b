import 'package:rapsap/controllers/cartcontrolller.dart';
import 'package:rapsap/view/widgets/commons.dart';

import '../../services/databaseHelper.dart';
import '../screens/cart_screen/cart_screen.dart';

class AnimatedCartCard extends StatefulWidget {
  final Widget child;
  final int delay;
  //milliseconds

  final double height;

  const AnimatedCartCard(
      {Key? key, required this.child, required this.height, this.delay = 10})
      : super(key: key);

  @override
  State<AnimatedCartCard> createState() => _AnimatedCartCardState();
}

class _AnimatedCartCardState extends State<AnimatedCartCard> {
  double height = 0;

  @override
  void initState() {
    // TODO: implement initState

    Future.delayed(const Duration(milliseconds: 10), () {
      setState(() {
        height = widget.height;
      });
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      width: double.infinity,
      decoration: const BoxDecoration(color: kwhite, boxShadow: [
        BoxShadow(
          color: kgrey,
          blurRadius: 5.0,
        )
      ]),
      duration: const Duration(milliseconds: 100),
      height: height,
      child: widget.child,
    );
  }
}

class AnimatedCartView extends StatelessWidget {
  const AnimatedCartView({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CartController>(
      builder: (_) {
        return _.myCartItems.isEmpty
            ? const SizedBox()
            : Align(
                alignment: Alignment.bottomCenter,
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(2),
                      topRight: Radius.circular(2)),
                  child: AnimatedCartCard(
                      height: 70,
                      child: Container(
                          color: kblue.withOpacity(0.86),
                          child: SizedBox(
                            height: 70,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  flex: 5,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Flexible(
                                        flex: 4,
                                        child: Text(
                                          '${_.myCartItems.fold<int>(0, (previousValue, element) => previousValue + element.qty!)} item',
                                          style: const TextStyle(
                                              fontWeight: FontWeight.w700,
                                              fontSize: 16,
                                              color: kwhite),
                                        ),
                                      ),
                                      const Flexible(flex: 1, child: kwidth5),
                                      const Flexible(
                                        flex: 1,
                                        child: Text(
                                          '•',
                                          style: TextStyle(
                                              color: kwhite,
                                              fontWeight: FontWeight.w700,
                                              fontSize: 16),
                                        ),
                                      ),
                                      const Flexible(flex: 1, child: kwidth5),
                                      FutureBuilder<double>(
                                          future: DatabaseHelper.instance
                                              .getSubTotal(),
                                          builder: (context,
                                              AsyncSnapshot<double> snapshot) {
                                            return Flexible(
                                              flex: 4,
                                              child: Text(
                                                "₹ ${snapshot.data != null ? snapshot.data!.round() : ""}",
                                                style: const TextStyle(
                                                    color: kwhite,
                                                    fontWeight: FontWeight.w700,
                                                    fontSize: 16),
                                              ),
                                            );
                                          })
                                    ],
                                  ),
                                ),
                                Flexible(
                                  flex: 5,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Flexible(
                                        child: InkWell(
                                          onTap: () {
                                            Get.to(() => const CartScreen());
                                            // _scrollController.animateTo(
                                            //   _scrollController
                                            //       .position.minScrollExtent,
                                            //   curve: Curves.easeOut,
                                            //   duration: const Duration(
                                            //       milliseconds: 500),
                                            // );
                                          },
                                          child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 8),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                  const Flexible(
                                                    flex: 4,
                                                    child: Text(
                                                      'View Cart',
                                                      style: TextStyle(
                                                          color: kwhite,
                                                          fontWeight:
                                                              FontWeight.w700,
                                                          fontSize: 18),
                                                      textAlign:
                                                          TextAlign.center,
                                                    ),
                                                  ),
                                                  const Flexible(
                                                      child: kwidth5),
                                                  Flexible(
                                                    child: SvgPicture.asset(
                                                      'assets/svg/cart-bag.svg',
                                                      // ignore: deprecated_member_use
                                                      color: kwhite,
                                                      height: 18,
                                                    ),
                                                  ),
                                                  const Flexible(
                                                    child: kwidth10,
                                                  )
                                                ],
                                              )),
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            ).paddingSymmetric(
                                horizontal: defaultpadding, vertical: 10),
                          ))),
                ));
      },
    );
  }
}
