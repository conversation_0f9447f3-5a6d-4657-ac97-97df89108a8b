import 'package:rapsap/controllers/categorycontroller.dart';
import 'package:rapsap/controllers/wishlistcontroller.dart';
import 'package:get/get.dart';
import 'package:rapsap/controllers/accountscontroller.dart';
import 'package:rapsap/controllers/cartcontrolller.dart';
import 'package:rapsap/controllers/home_view_controller.dart';
import 'package:rapsap/controllers/ordercontroller.dart';
import 'package:rapsap/controllers/product_view_controller.dart';
import 'package:rapsap/controllers/ratingscreencontroller.dart';
import 'package:rapsap/controllers/user_controller.dart';

class HomeBinding implements Bindings {
  @override
  void dependencies() {
    Get.put<UserController>(UserController());
    Get.put<HomeViewController>(HomeViewController());
    Get.put<CartController>(CartController());
    Get.put<ProductViewController>(ProductViewController());
    Get.put<OrderController>(OrderController());
    Get.lazyPut<AccountController>(() => AccountController());
    Get.put<RatingScrnController>(RatingScrnController());
    Get.put<Wishlistcontroller>(Wishlistcontroller());
    Get.put<CategoryController>(CategoryController());
  }
}
