//Colors

import 'package:rapsap/view/widgets/commons.dart';

//Colors
// const Color kblack = Color(0xFF000000);
const Color kblack = Color(0xFF161616);

const Color kwhite = Colors.white;
const Color kgrey = Color(0xFFC5C8CD);
const Color kblue = Color(0xFF5074F3);

//padding
const double defaultpadding = 16;

//asset
const String splashbackground = 'assets/images/splash_background.png';
const String logo = 'assets/images/logo.svg';
const String loginhead = 'assets/images/loginheadimg.png';
const String loginheadtitle = 'assets/images/logo_login_head.svg';

//height
const SizedBox kheight5 = SizedBox(height: 5);
const SizedBox kheight10 = SizedBox(height: 10);
const SizedBox kheight20 = SizedBox(height: 20);
const SizedBox kheight30 = SizedBox(height: 30);
const SizedBox kheight40 = SizedBox(height: 40);
const SizedBox kheight50 = SizedBox(height: 50);

//width
const SizedBox kwidth5 = SizedBox(width: 5);
const SizedBox kwidth10 = SizedBox(width: 10);
const SizedBox kwidth20 = SizedBox(width: 20);
const SizedBox kwidth30 = SizedBox(width: 30);
const SizedBox kwidth40 = SizedBox(width: 40);
const SizedBox kwidth50 = SizedBox(width: 50);

//Theme

const isProd = false;

const stagBaseurl = "https://rapsap-api.onrender.com/";
const prodBaseurl = "https://api-prod.rapsap.com/";

// const baseURL = 'https://rapsap-stage.herokuapp.com/';
const baseURL = isProd ? prodBaseurl : stagBaseurl;

//padding

String rapsaplogobase64 =
    "data:image/png;base64,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";

TextStyle headStyleNormal = const TextStyle(
  fontSize: 16,
  color: Color(0xFF000000),
);

TextStyle headStyLoginField = const TextStyle(
  fontSize: 16,
  color: Colors.black,
);
TextStyle headTextStyle = GoogleFonts.dmSans(
  fontSize: 18,
  fontWeight: FontWeight.w700,
  color: Colors.black,
);

final categorycolorlist = [
  CategoryColorModel(
      bgColor: const Color(0xffF1FFEF), txtColor: const Color(0xff0D9E00)),
  CategoryColorModel(
      bgColor: const Color(0xffFFE6E6), txtColor: const Color(0xffFF0000)),
  CategoryColorModel(
      bgColor: const Color(0xffE6EFFF), txtColor: const Color(0xff005DFF)),
  CategoryColorModel(
      bgColor: const Color(0xffFFF6E6), txtColor: const Color(0xffB77200)),
  CategoryColorModel(
      bgColor: const Color(0xffF2EFED), txtColor: const Color(0xff7A5649)),
  CategoryColorModel(
      bgColor: const Color(0xffFAFBE6), txtColor: const Color(0xff9AA000)),
  CategoryColorModel(
      bgColor: const Color(0xffFFEEE6), txtColor: const Color(0xffFD5100)),
  CategoryColorModel(
      bgColor: const Color(0xffF9E6EB), txtColor: const Color(0xffD40035)),
  CategoryColorModel(
      bgColor: const Color(0xffE6FAF4), txtColor: const Color(0xff00B881)),
];

class CategoryColorModel {
  final Color bgColor;
  final Color txtColor;

  CategoryColorModel({required this.bgColor, required this.txtColor});
}
