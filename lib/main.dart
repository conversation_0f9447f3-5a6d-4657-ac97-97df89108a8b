import 'dart:developer';
import 'package:rapsap/services/firebaseservices.dart';
import 'package:rapsap/services/logging_service.dart';
import 'package:rapsap/services/logging_config.dart';
import 'package:rapsap/services/navigation_logger.dart';
import 'package:rapsap/view/widgets/keyboardhider.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:rapsap/view/widgets/notification_service.dart';
import 'package:flutter/services.dart';
import 'package:flutter_displaymode/flutter_displaymode.dart';
import 'view/widgets/commons.dart';
// ignore: unused_import
import 'package:firebase_performance/firebase_performance.dart';

void main() async {
  // Ensure Flutter binding is initialized first
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Initialize logging first
    await LoggingService.instance.init();
    LoggingService.instance.log(LogLevel.info, '🚀 App startup initiated');
    
    // Initialize storage
    await GetStorage.init();
    await GetStorage.init('payload');

    // Initialize logging system
    await LoggingConfig.instance.init();
    await LoggingService.instance.init();

    // Set display mode for supported platforms
    if (!kIsWeb) {
      try {
        await FlutterDisplayMode.setHighRefreshRate();
      } catch (e) {
        log('FlutterDisplayMode not supported on this platform: $e');
      }
    }

    // CRITICAL: Initialize Firebase with enhanced error handling
    bool firebaseInitialized = await FirebaseService.init();
    if (firebaseInitialized) {
      log('Firebase initialized successfully in main()');
    } else {
      log('Firebase initialization failed, but app will continue');
    }

    // Initialize other dependencies
    HomeBinding().dependencies();
    
    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    
    // Run the app
    runApp(const Rapsap());
    
  } catch (e, stackTrace) {
    log('Error in main initialization: $e');
    log('Stack trace: $stackTrace');
    
    // Even if there are errors, try to run the app
    try {
      runApp(const Rapsap());
    } catch (appError) {
      log('Failed to start app: $appError');
    }
  }
}

GetStorage storage = GetStorage();

class Rapsap extends StatelessWidget {
  const Rapsap({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardHider(
      child: GetMaterialApp(
        onInit: () async {
          // Setup notification interactions with error handling
          try {
            await NotificationService().setupInteractedMessage();
            log('Notification interaction setup completed');
          } catch (e) {
            log('Error setting up notification interactions: $e');
          }
        },
        navigatorObservers: [
          ...FirebaseService.analyticsObserver,
          NavigationLogger(),
        ],
        defaultTransition: Transition.rightToLeft,
        initialBinding: HomeBinding(),
        theme: RapsapTheme.theme,
        debugShowCheckedModeBanner: false,
        home: const SplashScreen(),
      ),
    );
  }
}