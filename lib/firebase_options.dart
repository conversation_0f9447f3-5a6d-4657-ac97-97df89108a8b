// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBanczDsWbk7A1GMH3IfKXfPSJYBjNXHOo',
    appId: '1:284272409634:web:6ffbc5faec6b058be55e7b',
    messagingSenderId: '284272409634',
    projectId: 'consumer-rapsap-com',
    authDomain: 'consumer-rapsap-com.firebaseapp.com',
    storageBucket: 'consumer-rapsap-com.firebasestorage.app',
    measurementId: 'G-JQWEFZ4ZSW',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAoeGMu9ZRZpATgVvO0oAtO9JEqfPdYoLg',
    appId: '1:284272409634:android:151fb8f6e4d69bd8e55e7b',
    messagingSenderId: '284272409634',
    projectId: 'consumer-rapsap-com',
    storageBucket: 'consumer-rapsap-com.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA4THQwunMiZZVPuDv2ntWSiw3lr22BLp8',
    appId: '1:284272409634:ios:4ee5a1e51b54c362e55e7b',
    messagingSenderId: '284272409634',
    projectId: 'consumer-rapsap-com',
    storageBucket: 'consumer-rapsap-com.firebasestorage.app',
    iosBundleId: 'com.rapsap.app',
  );

}