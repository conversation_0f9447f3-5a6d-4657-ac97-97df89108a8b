import 'dart:convert';
import 'dart:developer';
import 'package:rapsap/model/homepageproducts/home_page_products/home_page_products.dart';
import 'package:rapsap/services/http_logging_client.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:http/http.dart' as http;
import 'package:rapsap/model/CategoryModel/category_model.dart';
import 'package:rapsap/model/SubcategoryModel/sub_category_model/sub_category_model.dart';

import '../main.dart';
import '../model/BannerAds/banner_ads.dart';
import '../model/category_product_model/category_products_model/category_products_model.dart';

class CategoryService {
  static Future<CategoryModel?> getCategoryList() async {
    String url = '${baseURL}api/v1/ecomm/getCategory';

    Map<String, dynamic> body = {
      "key": "JWT",
      "secret": "RAPSAP",
    };
    Map<String, String> headers = {};

    try {
      final response = await LoggedHttpClient.post(
        Uri.parse(url),
        body: body,
        headers: headers,
      );

      if (response.statusCode == 200) {
        final responseModel = categoryModelFromJson(response.body);
        return responseModel;
      }
      return null;
    } catch (e) {
      log("CategoryService error: $e");
    }
    return null;
  }

  static Future<SubCategoryModel?> getSUbCategoryList({categoryId}) async {
    String url = '${baseURL}api/v1/ecomm/getSubcategory';

    Map<String, dynamic> body = {
      "key": "JWT",
      "secret": "RAPSAP",
      "category_id": categoryId.toString()
    };
    Map<String, String> headers = {};

    try {
      final response = await http.post(
        Uri.parse(url),
        body: body,
        headers: headers,
      );

      log("status:::${response.statusCode}");

      if (response.statusCode == 200) {
        log(response.body.toString());
        final responseModel = SubCategoryModel.fromJson(response.body);
        return responseModel;
      }
      return null;
    } catch (e) {
      log("$e");
    }
    return null;
  }

  static Future<BannerAds?> getBannerAds() async {
    String url = '${baseURL}api/v1/ecomm/getBanner';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };
    var client = http.Client();

    BannerAds? bannerAds;
    try {
      var response = await client.post(Uri.parse(url),
          body: json.encode({}), headers: headers);
      log("status:::${response.statusCode}");

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        // Get.to(() => LoginNSkipPage());
        log('jsonRes $jsonRes');
      } else {
        log('here--- ${jsonResponse['data']}');
        bannerAds = BannerAds.fromJson(jsonResponse);
        return bannerAds;
      }
    } catch (e, stackTrace) {
      log('Error in $url =>:: $e  \nStackTrace: $stackTrace');
      return bannerAds;
    }
    return null;
  }

  static Future<CategoryProductModel?> getPrioductsyoumaylike() async {
    String url = "${baseURL}api/v1/ecomm/productsMayLikes";
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      // 'Content-type': 'application/json'
    };
    Map<String, dynamic> body = {};

    body = {"store_id": "1"};

    try {
      final response =
          await http.post(Uri.parse(url), body: body, headers: headers);

      if (response.statusCode == 200) {
        final responseModel = CategoryProductModel.fromJson(response.body);
        log(responseModel.data.toString());
        return responseModel;
      }

      return null;
    } catch (e) {
      log("$e");
    }
    return null;
  }

  static Future<CategoryProductModel?> getProductsByCategory(
      {required int categoryId,
      String? type,
      int page = 0,
      String? subcategoryid,
      String? filter}) async {
    String url = "${baseURL}api/v1/ecomm/getProductsByCategory";
    Map<String, dynamic> body = {};
    final UserController controller = Get.find<UserController>();
    if (filter != "" && filter != null) {
      if (type != "sub_category") {
        if (controller.userdata.value.data != null) {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "user_id": controller.userdata.value.data!.id.toString(),
            "page": page.toString(),
            "key": "JWT",
            "secret": "RAPSAP",
            "filter": filter
          };
        } else {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "page": page.toString(),
            "key": "JWT",
            "secret": "RAPSAP",
            "filter": filter
          };
        }
      } else {
        if (controller.userdata.value.data != null) {
          body = {
            "category_id": categoryId.toString(),
            "type": "sub_category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "user_id": controller.userdata.value.data!.id.toString(),
            "page": page.toString(),
            "sub_category_id": subcategoryid,
            "key": "JWT",
            "secret": "RAPSAP",
          };
        } else {
          body = {
            "category_id": categoryId.toString(),
            "type": "sub_category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "page": page.toString(),
            "sub_category_id": subcategoryid,
            "key": "JWT",
            "secret": "RAPSAP",
            "filter": filter
          };
        }
      }
    } else {
      if (type == "home") {
        if (controller.userdata.value.data != null) {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "page": page.toString(),
            "user_id": controller.userdata.value.data!.id.toString(),
            "size": "10",
            "key": "JWT",
            "secret": "RAPSAP"
          };
        } else {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "page": page.toString(),
            "size": "10",
            "key": "JWT",
            "secret": "RAPSAP"
          };
        }
      } else if (type != "sub_category") {
        if (controller.userdata.value.data != null) {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "user_id": controller.userdata.value.data!.id.toString(),
            "page": page.toString(),
            "key": "JWT",
            "secret": "RAPSAP"
          };
        } else {
          body = {
            "category_id": categoryId.toString(),
            "type": "category",
            "store_id": (storage.read('storeID') ?? 0).toString(),
            "page": page.toString(),
            "key": "JWT",
            "secret": "RAPSAP"
          };
        }
      } else {
        body = {
          "category_id": categoryId.toString(),
          "type": "sub_category",
          "store_id": (storage.read('storeID') ?? 0).toString(),
          "page": page.toString(),
          "sub_category_id": subcategoryid,
          "key": "JWT",
          "secret": "RAPSAP"
        };
      }
    }

    try {
      final response = await http.post(
        Uri.parse(url),
        body: body,
      );

      if (response.statusCode == 200) {
        final responseModel = CategoryProductModel.fromJson(response.body);
        log("jsonEncode(responseModel.data.toString() ${responseModel.toJson()}");
        return responseModel;
      }

      return null;
    } catch (e) {
      log("$e");
    }
    return null;
  }

  static Future<HomePageProducts?> getHomePageProductsByCategory() async {
    String url = "${baseURL}api/v1/ecomm/getFiveProductsPerCategory";
    final UserController controller = Get.find<UserController>();

    Map<String, dynamic> body = {};
    log(storage.read('storeID'));
    if (controller.userdata.value.data == null) {
      body = {
        "key": "JWT",
        "store_id": (storage.read('storeID') ?? 0).toString(),
        "secret": "RAPSAP"
      };
    } else {
      body = {
        "key": "JWT",
        "store_id": (storage.read('storeID') ?? 0).toString(),
        "secret": "RAPSAP",
        "user_id": controller.userdata.value.data!.id.toString()
      };
    }

    try {
      final response = await http.post(
        Uri.parse(url),
        body: body,
      );

      if (response.statusCode == 200) {
        final responseModel = HomePageProducts.fromJson(response.body);
        log(responseModel.data.toString());
        return responseModel;
      }

      return null;
    } catch (e) {
      log(" error $e");
    }
    return null;
  }
}
