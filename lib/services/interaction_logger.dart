import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rapsap/services/logging_service.dart';

/// Utility class for logging user interactions
class InteractionLogger {
  static final LoggingService _logger = LoggingService.instance;

  /// Log button tap interaction
  static void logButtonTap({
    required String buttonName,
    String? screen,
    Map<String, dynamic>? additionalData,
  }) {
    _logger.logInteraction(
      type: 'tap',
      element: buttonName,
      screen: screen ?? Get.currentRoute,
      data: additionalData,
    );
  }

  /// Log form submission
  static void logFormSubmit({
    required String formName,
    String? screen,
    Map<String, dynamic>? formData,
    bool? success,
  }) {
    final data = <String, dynamic>{
      if (formData != null) 'formData': _sanitizeFormData(formData),
      if (success != null) 'success': success,
    };

    _logger.logInteraction(
      type: 'form_submit',
      element: formName,
      screen: screen ?? Get.currentRoute,
      data: data.isNotEmpty ? data : null,
    );
  }

  /// Log text input interaction
  static void logTextInput({
    required String fieldName,
    String? screen,
    int? textLength,
    String? inputType,
  }) {
    final data = <String, dynamic>{
      if (textLength != null) 'textLength': textLength,
      if (inputType != null) 'inputType': inputType,
    };

    _logger.logInteraction(
      type: 'input',
      element: fieldName,
      screen: screen ?? Get.currentRoute,
      data: data.isNotEmpty ? data : null,
    );
  }

  /// Log search interaction
  static void logSearch({
    required String searchTerm,
    String? screen,
    int? resultsCount,
    String? category,
  }) {
    final data = <String, dynamic>{
      'searchTerm': searchTerm.length > 50 ? '${searchTerm.substring(0, 50)}...' : searchTerm,
      if (resultsCount != null) 'resultsCount': resultsCount,
      if (category != null) 'category': category,
    };

    _logger.logInteraction(
      type: 'search',
      element: 'search_field',
      screen: screen ?? Get.currentRoute,
      data: data,
    );
  }

  /// Log swipe gesture
  static void logSwipe({
    required String direction,
    required String element,
    String? screen,
  }) {
    _logger.logInteraction(
      type: 'swipe',
      element: element,
      screen: screen ?? Get.currentRoute,
      data: {'direction': direction},
    );
  }

  /// Log scroll interaction
  static void logScroll({
    required String element,
    String? screen,
    double? scrollPosition,
    String? direction,
  }) {
    final data = <String, dynamic>{
      if (scrollPosition != null) 'scrollPosition': scrollPosition,
      if (direction != null) 'direction': direction,
    };

    _logger.logInteraction(
      type: 'scroll',
      element: element,
      screen: screen ?? Get.currentRoute,
      data: data.isNotEmpty ? data : null,
    );
  }

  /// Log item selection (dropdown, list, etc.)
  static void logItemSelection({
    required String element,
    required String selectedItem,
    String? screen,
    int? selectedIndex,
  }) {
    final data = <String, dynamic>{
      'selectedItem': selectedItem,
      if (selectedIndex != null) 'selectedIndex': selectedIndex,
    };

    _logger.logInteraction(
      type: 'selection',
      element: element,
      screen: screen ?? Get.currentRoute,
      data: data,
    );
  }

  /// Log cart operations
  static void logCartOperation({
    required String operation, // 'add', 'remove', 'update', 'clear'
    String? productId,
    String? productName,
    int? quantity,
    double? price,
    String? screen,
  }) {
    final data = <String, dynamic>{
      'operation': operation,
      if (productId != null) 'productId': productId,
      if (productName != null) 'productName': productName,
      if (quantity != null) 'quantity': quantity,
      if (price != null) 'price': price,
    };

    _logger.logInteraction(
      type: 'cart_operation',
      element: 'cart',
      screen: screen ?? Get.currentRoute,
      data: data,
    );
  }

  /// Log wishlist operations
  static void logWishlistOperation({
    required String operation, // 'add', 'remove'
    String? productId,
    String? productName,
    String? screen,
  }) {
    final data = <String, dynamic>{
      'operation': operation,
      if (productId != null) 'productId': productId,
      if (productName != null) 'productName': productName,
    };

    _logger.logInteraction(
      type: 'wishlist_operation',
      element: 'wishlist',
      screen: screen ?? Get.currentRoute,
      data: data,
    );
  }

  /// Log filter/sort operations
  static void logFilterSort({
    required String type, // 'filter', 'sort'
    required String criteria,
    String? screen,
    Map<String, dynamic>? filterValues,
  }) {
    final data = <String, dynamic>{
      'type': type,
      'criteria': criteria,
      if (filterValues != null) 'filterValues': filterValues,
    };

    _logger.logInteraction(
      type: 'filter_sort',
      element: '${type}_control',
      screen: screen ?? Get.currentRoute,
      data: data,
    );
  }

  /// Log payment interactions
  static void logPaymentInteraction({
    required String action, // 'initiate', 'success', 'failure', 'cancel'
    String? paymentMethod,
    double? amount,
    String? orderId,
    String? screen,
  }) {
    final data = <String, dynamic>{
      'action': action,
      if (paymentMethod != null) 'paymentMethod': paymentMethod,
      if (amount != null) 'amount': amount,
      if (orderId != null) 'orderId': orderId,
    };

    _logger.logInteraction(
      type: 'payment',
      element: 'payment_system',
      screen: screen ?? Get.currentRoute,
      data: data,
    );
  }

  /// Log rating/review interactions
  static void logRatingReview({
    required String action, // 'rate', 'review', 'submit'
    String? productId,
    int? rating,
    bool? hasReview,
    String? screen,
  }) {
    final data = <String, dynamic>{
      'action': action,
      if (productId != null) 'productId': productId,
      if (rating != null) 'rating': rating,
      if (hasReview != null) 'hasReview': hasReview,
    };

    _logger.logInteraction(
      type: 'rating_review',
      element: 'rating_system',
      screen: screen ?? Get.currentRoute,
      data: data,
    );
  }

  /// Sanitize form data to remove sensitive information
  static Map<String, dynamic> _sanitizeFormData(Map<String, dynamic> formData) {
    final sanitized = <String, dynamic>{};
    final sensitiveFields = [
      'password', 'pin', 'otp', 'cvv', 'card_number', 'account_number',
      'ssn', 'social_security', 'credit_card', 'debit_card'
    ];

    formData.forEach((key, value) {
      final keyLower = key.toLowerCase();
      if (sensitiveFields.any((field) => keyLower.contains(field))) {
        sanitized[key] = '[REDACTED]';
      } else if (value is String && value.length > 100) {
        sanitized[key] = '${value.substring(0, 100)}... [TRUNCATED]';
      } else {
        sanitized[key] = value;
      }
    });

    return sanitized;
  }
}

/// Widget wrapper that automatically logs tap interactions
class LoggedTapWrapper extends StatelessWidget {
  final Widget child;
  final String elementName;
  final VoidCallback? onTap;
  final Map<String, dynamic>? additionalData;
  final String? screen;

  const LoggedTapWrapper({
    Key? key,
    required this.child,
    required this.elementName,
    this.onTap,
    this.additionalData,
    this.screen,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        InteractionLogger.logButtonTap(
          buttonName: elementName,
          screen: screen,
          additionalData: additionalData,
        );
        onTap?.call();
      },
      child: child,
    );
  }
}

/// Extension on Widget to add logging capabilities
extension WidgetLoggingExtension on Widget {
  /// Wrap widget with tap logging
  Widget withTapLogging({
    required String elementName,
    VoidCallback? onTap,
    Map<String, dynamic>? additionalData,
    String? screen,
  }) {
    return LoggedTapWrapper(
      elementName: elementName,
      onTap: onTap,
      additionalData: additionalData,
      screen: screen,
      child: this,
    );
  }
}
