import 'dart:developer';

import 'package:rapsap/main.dart';
import 'package:rapsap/model/searchModel/search_model/search_model.dart';
import 'package:rapsap/services/http_logging_client.dart';

import '../view/widgets/commons.dart';
import 'package:http/http.dart' as http;

class SearchService {
  static Future<SearchModel?> getProducts(query, page) async {
    String url = '${baseURL}api/v1/ecomm/getProducts';

    Map<String, dynamic> body = {
      "key": "JWT",
      "secret": "RAPSAP",
      "page": page.toString(),
      "keyword": query,
      "type": "user",
      "store_id": (storage.read("storeID") ?? 0).toString()
    };
    Map<String, String> headers = {};

    try {
      final response = await LoggedHttpClient.post(
        Uri.parse(url),
        body: body,
        headers: headers,
      );

      if (response.statusCode == 200) {
        final responseModel = SearchModel.fromJson(response.body);
        return responseModel;
      }
      return null;
    } catch (e) {
      // Error is already logged by LoggedHttpClient
      log("SearchService error: $e");
    }
    return null;
  }
}
