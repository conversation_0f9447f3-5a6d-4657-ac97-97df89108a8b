import 'dart:developer';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:rapsap/controllers/cartcontrolller.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseHelper {
  DatabaseHelper._privateConstructor();
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();

  static Database? _database;
  Future<Database> get database async => _database ??= await _initDatabase();

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'rapsap.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE products(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          date INTEGER,
          name TEXT,
          qty INTEGER,
          productID INTEGER,
          variantID INTEGER,
          imageURL TEXT,
          weight FLOAT,
          price FLOAT,
          isFree INTEGER,
          mrp FLOAT
      )
      ''');

    await db.execute('''
      CREATE TABLE orders(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          addressID INTEGER,
          storeID INTEGER,
          subTotal FLOAT,
          grandTotal FLOAT,
          discount FLOAT,
          deliveryCharge FLOAT,
          tax FLOAT,
          orderID INTEGER,
          offerID INTEGER,
          minOrder FLOAT,
          couponCode TEXT
      )
      ''');

    await db.execute('''
      CREATE TABLE payment(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          razorOrderID TEXT,
          razorInvoiceID INTEGER,
          status TEXT,
          razorKey TEXT
      )
      ''');

    await db.execute('''
      CREATE TABLE userAddress(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT,
          addressType TEXT,
          isDefault INTEGER,
          phone TEXT,
          address1 TEXT,
          address2 TEXT,
          city TEXT,
          state TEXT,
          pincode TEXT,
          latitude TEXT,
          longitude TEXT,
          addressID INTEGER
      )
      ''');
  }

  Future<List<ShopingCart>> getGroceries() async {
    Database db = await instance.database;
    var products = await db.query('products', orderBy: 'id');
    List<ShopingCart> groceryList = products.isNotEmpty
        ? products.map((c) => ShopingCart.fromMap(c)).toList()
        : [];
    final CartController cartController = Get.find();
    cartController.myCartItems.value = groceryList;
    cartController.update();
    return groceryList;
  }

  Future<int> add(ShopingCart cart) async {
    Database db = await instance.database;
    return await db.insert('products', cart.toMap());
  }

  Future<int> insertAddress(UserAddress address) async {
    Database db = await instance.database;
    await db.rawQuery('DELETE FROM userAddress');
    return await db.insert('userAddress', address.toMap());
  }

  Future<List<UserAddress>> getUserDbAddress() async {
    Database db = await instance.database;
    var address = await db.query('userAddress', orderBy: 'name');
    List<UserAddress> addressList = address.isNotEmpty
        ? address.map((c) => UserAddress.fromMap(c)).toList()
        : [];
    return addressList;
  }

  Future<int> addUpdate(ShopingCart cart) async {
    // check existing
    if (kDebugMode) {
      print(cart.toMap());
    }
    Database db = await instance.database;
    List<Map> check = await db.rawQuery(
        'select qty from products where variantID=?', [cart.variantID]);

    if (check.isNotEmpty) {
      // updated here
      List<Map> list = await db.rawQuery(
          'update products set qty =? where variantID=?',
          [check[0]['qty'] + 1, cart.variantID]);
      await DatabaseHelper.instance.getGroceries();
      return 1;
    } else {
      // to create new
      // Database db = await instance.database;
      final value = await db.insert('products', cart.toMap());
      DatabaseHelper.instance.getGroceries();

      return value;
    }
  }

  Future<int> removeUpdate(ShopingCart cart) async {
    // check existing
    Database db = await instance.database;
    List<Map> check = await db.rawQuery(
        'select qty from products where variantID=?', [cart.variantID]);
    if (kDebugMode) {
      print('check ${check[0]['qty']}');
    }
    if (check[0]['qty'] > 1) {
      // updated here
      List<Map> list = await db.rawQuery(
          'update products set qty =? where variantID=?',
          [check[0]['qty'] - 1, cart.variantID]);
      DatabaseHelper.instance.getGroceries();

      return 1;
    } else {
      await db.delete('products',
          where: 'variantID = ?', whereArgs: [cart.variantID]);
      DatabaseHelper.instance.getGroceries();

      // to create new
      if (kDebugMode) {
        print('nothing is going to happne');
      }
      return 0;
    }
  }

  Future<int> remove(int id) async {
    Database db = await instance.database;
    // return await db.delete('products', where: 'id = ?', whereArgs: [id]);
    return await db.delete('products', where: 'variantID = ?', whereArgs: [id]);
  }

  Future<int> removeFreeItem() async {
    Database db = await instance.database;
    // return await db.delete('products', where: 'id = ?', whereArgs: [id]);
    return await db.delete('products', where: 'isFree = ?', whereArgs: [1]);
  }

  Future<int> update(ShopingCart cart) async {
    Database db = await instance.database;
    return await db.update('products', cart.toMap(),
        where: "variantID = ?", whereArgs: [cart.variantID]);
  }

  Future increseQty(ShopingCart cart) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery(
        'update products set qty =? where variantID=?',
        [cart.qty, cart.variantID]);
    DatabaseHelper.instance.getGroceries();

    return list.isNotEmpty ? list[0]['qty'] : 0;
  }

  Future decreseQty(ShopingCart cart) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery(
        'update products set qty =? where variantID=?',
        [cart.qty, cart.variantID]);
    DatabaseHelper.instance.getGroceries();

    return list.isNotEmpty ? list[0]['qty'] : 0;
  }

  Future<int> getQty(ShopingCart cart) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery(
        'select qty from products where variantID=?', [cart.variantID]);

    return list.isNotEmpty ? list[0]['qty'] : 0;
  }

  Future<double> getSubTotal() async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery(
        'select sum(price * qty) as subtotal from products where isFree is null');
    // print('subtotal ${list[0]}');
    if (list[0]['subtotal'] == null) {
      await db.rawQuery('update orders set subTotal =?', [0.0]);
      return 0.0;
    } else {
      var sub = list.isNotEmpty ? list[0]['subtotal'] : 0.0;
      log(sub.toString());
      final pr = await db.rawQuery("select * from orders");

      if (pr.isNotEmpty) {
        // print('object');
        // print('sub=$sub');
        await db.rawQuery(
            "update orders set subTotal =? where id=?", [sub, pr.first['id']]);
        final olist = await db.query('orders');
        // print("orderlist=$olist");
      } else {
        await db.rawQuery("insert into orders (subTotal) values(?) ", [sub]);
      }
      // final data = await db.rawQuery('update orders set subTotal =?', [sub]);
      // final pr = await db.rawQuery("select * from orders");
      // log("orders=${pr.toString()}");
      // await db.rawQuery('update orders set subTotal =?', [sub]);
      return sub;
    }
  }

  Future<int> orderAddUpdate(Orders orders) async {
    // check existing
    Database db = await instance.database;

    return await db.insert('orders', orders.toMap());
  }

  Future<List<Orders>> getDbOrder() async {
    await Future.delayed(const Duration(milliseconds: 200));
    Database db = await instance.database;
    var orders = await db.query('orders', orderBy: 'id');
    List<Orders> ordersList =
        orders.isNotEmpty ? orders.map((c) => Orders.fromMap1(c)).toList() : [];
    return ordersList;
  }

  Future<List<Orders>> getDbOrder1() async {
    await Future.delayed(const Duration(milliseconds: 200));
    Database db = await instance.database;
    var orders = await db.query('orders', orderBy: 'id');
    List<Orders> ordersList =
        orders.isNotEmpty ? orders.map((c) => Orders.fromMap1(c)).toList() : [];
    return ordersList;
  }

  Future<List<Orders>> getDbOrder2() async {
    Database db = await instance.database;
    var orders = await db.query('orders', orderBy: 'id');
    List<Orders> ordersList =
        orders.isNotEmpty ? orders.map((c) => Orders.fromMap1(c)).toList() : [];
    return ordersList;
  }

  Future setOrderID(int id) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('update orders set orderID =?', [id]);

    return list.isNotEmpty ? list[0]['orderID'] : 0;
  }

  Future setAddressID(int id) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('update orders set addressID =?', [id]);

    return list.isNotEmpty ? list[0]['addressID'] : 0;
  }

  Future setDiscountValue(
      double discount, int offerID, double minOrder, String couponCode) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery(
        'update orders set discount =?, offerID=?, minOrder=?,couponCode=?',
        [discount, offerID, minOrder, couponCode]);

    getGroceries();

    return list.isNotEmpty ? list[0]['discount'] : 0;
  }

  Future setStoreID(int storeid) async {
    Database db = await instance.database;
    List<Map> list =
        await db.rawQuery('update orders set storeID =?', [storeid]);

    return list.isNotEmpty ? list[0]['storeID'] : 0;
  }

  Future clearShopingCart() async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('DELETE FROM products');

    return 1;
  }

  Future clearAllOrders() async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('DELETE FROM orders');

    return 1;
  }

  Future setPaymentDetails(
      String orderid, String invID, String status, String key) async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery(
        'insert into payment (razorOrderID,razorInvoiceID,status, razorKey) values (?,?,?,?)',
        [orderid, invID, status, key]);
    return true;
  }

  Future getPaymentDetails() async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('select * from payment');
    return list;
  }

  Future clearPayment() async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('DELETE FROM payment');
    return 1;
  }

  Future clearAddress() async {
    Database db = await instance.database;
    List<Map> list = await db.rawQuery('DELETE FROM address');
    return 1;
  }
}

class ShopingCart {
  final int? id;
  final String? name;
  final int? qty;
  final int productID;
  final int variantID;
  final String? imageURL;
  final double? weight;
  final double? price;
  final int? isFree;
  final double? mrp;

  final int? dateTime;

  ShopingCart(
      {this.id,
      this.name,
      this.qty,
      required this.productID,
      required this.variantID,
      this.imageURL,
      this.weight,
      this.price,
      this.isFree,
      this.mrp,
      dateTime})
      : dateTime = (dateTime ?? DateTime.now().millisecondsSinceEpoch);

  factory ShopingCart.fromMap(Map<String, dynamic> json) => ShopingCart(
      id: json['id'],
      dateTime: json["date"] ?? DateTime.now(),
      name: json['name'],
      qty: json['qty'],
      productID: json['productID'],
      variantID: json['variantID'],
      imageURL: json['imageURL'],
      weight: json['weight'],
      price: json['price'],
      isFree: json['isFree'],
      mrp: json['mrp']);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': dateTime,
      'name': name,
      'qty': qty,
      'productID': productID,
      'variantID': variantID,
      'imageURL': imageURL,
      'weight': weight,
      'price': price,
      'isFree': isFree,
      'mrp': mrp
    };
  }
}

class UserAddress {
  final int? id;
  final String? name;
  final String? addressType;
  final int? isDefault;
  final String? phone;
  final String? address1;
  final String? address2;
  final String? city;
  final String? state;
  final String? pincode;
  final String? latitude;
  final String? longitude;
  final int? addressID;

  UserAddress({
    this.id,
    this.name,
    this.address1,
    this.address2,
    this.addressID,
    this.addressType,
    this.city,
    this.isDefault,
    this.latitude,
    this.longitude,
    this.phone,
    this.pincode,
    this.state,
  });

  factory UserAddress.fromMap(Map<String, dynamic> json) => UserAddress(
        id: json['id'],
        name: json['name'],
        address1: json['address1'],
        address2: json['address2'],
        addressID: json['addressID'],
        addressType: json['addressType'],
        city: json['city'],
        isDefault: json['isDefault'],
        latitude: json['latitude'],
        longitude: json['longitude'],
        phone: json['phone'],
        pincode: json['pincode'],
        state: json['state'],
      );

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'address1': address1,
      'address2': address2,
      'addressID': addressID,
      'addressType': addressType,
      'city': city,
      'isDefault': isDefault,
      'latitude': latitude,
      'longitude': longitude,
      'phone': phone,
      'pincode': pincode,
      'state': state,
    };
  }
}

class Orders {
  final int? id;
  final int? addressID;
  final int? storeID;
  final double? subTotal;
  final double? grandTotal;
  final double? discount;
  final double? deliveryCharge;
  final double? tax;
  final int? orderID;
  final int? offerID;
  final double? minOrder;
  final String? couponCode;

  Orders({
    this.id,
    this.addressID,
    this.deliveryCharge,
    this.discount,
    this.subTotal,
    this.grandTotal,
    this.storeID,
    this.tax,
    this.orderID,
    this.offerID,
    this.minOrder,
    this.couponCode,
  });

  factory Orders.fromMap(Map<String, dynamic> json) => Orders(
        id: json['id'],
        addressID: json['addressID'],
        deliveryCharge: json['deliveryCharge'],
        discount: json['discount'],
        subTotal: json['subTotal'],
        tax: json['tax'],
        grandTotal: json['grandTotal'],
        storeID: json['storeID'],
        orderID: json['orderID'],
        offerID: json['offerID'],
        minOrder: json['minOrder'],
        couponCode: json['couponCode'],
      );
  factory Orders.fromMap1(Map<String, dynamic> json) => Orders(
      id: json['id'],
      addressID: json['addressID'],
      deliveryCharge: json['deliveryCharge'],
      discount: json['discount'],
      subTotal: json['subTotal'],
      tax: json['tax'],
      grandTotal: json['grandTotal'],
      storeID: json['storeID'],
      orderID: json['orderID'],
      offerID: json['offerID'],
      minOrder: json['minOrder'],
      couponCode: json['couponCode']);

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'addressID': addressID,
      'storeID': storeID,
      'deliveryCharge': deliveryCharge,
      'discount': discount,
      'subTotal': subTotal,
      'grandTotal': grandTotal,
      'tax': tax,
      'orderID': orderID,
      'offerID': offerID,
      'minOrder': minOrder,
      'couponCode': couponCode
    };
  }
}
