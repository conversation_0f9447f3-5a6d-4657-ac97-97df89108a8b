import 'dart:convert';
import 'dart:developer';

import 'package:http/http.dart' as http;
import 'package:rapsap/view/widgets/commons.dart';
import 'package:rapsap/main.dart';
import 'package:rapsap/model/productmodel/products/products.dart';
import 'package:rapsap/model/product_review_model/product_review_model.dart';
import 'package:rapsap/model/user_model/usermodel.dart';

import '../controllers/ratingscreencontroller.dart';

class ProductService {
  static Future<ProductReviewModel?> getProductReviews(
      {required int productId, int? rating}) async {
    String url = "https://rapsap-api.herokuapp.com/api/v1/ecomm/getReview";
    Map<String, dynamic> body = {};
    if (rating != 0) {
      body = {
        "type": "user",
        "product_id": productId.toString(),
        "page": "0",
        "keyword": rating.toString(),
        "size": "10",
        "key": "JWT",
        "secret": "RAPSAP",
      };
    } else {
      body = {
        "type": "user",
        "product_id": productId.toString(),
        "page": "0",
        "size": "10",
        "key": "JWT",
        "secret": "RAPSAP",
      };
    }

    try {
      final res = await http.post(Uri.parse(url), body: body);

      if (res.statusCode == 200) {
        log("boooodyyyyyyyy${res.body}");
        final response = productReviewModelFromJson(res.body);
        return response;
      } else {
        return null;
      }
    } catch (e) {
      log("Error:: $e");
    }
    return null;
  }

  static Future<Products?> getProductbyID({required int productId}) async {
    String url = "${baseURL}api/v1/ecomm/getProductByID";
    log("productid=$productId");
    Map<String, dynamic> body = {};
    // log(UserData.fromJson(storage.read('userdata')).data!.id.toString());
    if (storage.read('userdata') != null) {
      body = {
        "product_id": productId.toString(),
        "type": "user",
        "store_id": (storage.read('storeID') ?? 0).toString(),
        "user_id":
            UserData.fromJson(storage.read('userdata')).data!.id.toString(),
        "key": "JWT",
        "secret": "RAPSAP"
      };
    } else {
      body = {
        "product_id": productId.toString(),
        "type": "user",
        "store_id": "1",
        "key": "JWT",
        "secret": "RAPSAP"
      };
    }

    try {
      final res = await http.post(Uri.parse(url), body: body);

      if (res.statusCode == 200) {
        log("productsbyid=${res.body}");
        final response = Products.fromJson(res.body);
        log("response=$response");
        return response;
      } else {
        return null;
      }
    } catch (e) {
      log("Error:: $e");
    }
    return null;
  }

  static Future updateReview(params) async {
    String url = '${baseURL}api/v1/ecomm/updateReview';
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    log('JWt $jwt');

    // UserController usertCTRL = Get.find();

    Map<String, dynamic> body = {
      'rating': params['rating'].toString(),
      'description': params['description'],
      'review_id': params['review_id']
    };
    Map<String, String> headers = {'Authorization': 'Bearer $jwt'};
    log('body::: $body');
    try {
      final response = await http.Client()
          .post(Uri.parse(url), body: body, headers: headers);
      // const jsonResponse = json.decode(response);
      log("response++=$response");
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        log('here-$jsonMap');

        return jsonMap;
      }
    } catch (e, stackTrace) {
      log('Error in $url =>:: $e  \nStackTrace: $stackTrace');
    }
    return null;
  }

  static Future saveRatingAndReview(params) async {
    String url = '${baseURL}api/v1/ecomm/createReview';
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    log('JWt $jwt');

    UserController usertCTRL = Get.find();

    Map<String, dynamic> body = {
      'rating': params['rating'].toString(),
      'description': params['description'],
      'user_id': usertCTRL.userdata.value.data!.id.toString(),
      'product_id': params['product_id'],
      'order_id': params['order_id']
    };
    Map<String, String> headers = {'Authorization': 'Bearer $jwt'};
    log('body::: $body');
    try {
      final response = await http.Client()
          .post(Uri.parse(url), body: body, headers: headers);
      // const jsonResponse = json.decode(response);
      log("response++=$response");
      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        log('here-$jsonMap');

        return jsonMap;
      }
    } catch (e, stackTrace) {
      final RatingScrnController rController = Get.find<RatingScrnController>();
      rController.buttonLoading.value = false;

      log('Error in $url =>:: $e  \nStackTrace: $stackTrace');
    }
    return null;
  }
}
