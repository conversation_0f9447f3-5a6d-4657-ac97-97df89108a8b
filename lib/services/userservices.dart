import 'dart:convert';
import 'dart:developer';

import 'package:rapsap/controllers/accountscontroller.dart';
import 'package:rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:rapsap/view/screens/launchingsoonpage.dart';
import 'package:flutter/foundation.dart';
import 'package:rapsap/view/screens/root_page/root_page.dart';

import '../model/rewards/rewardsmode.dart';
import '../view/widgets/commons.dart';
import '../main.dart';
import '../model/user_model/usermodel.dart';
import 'package:http/http.dart' as http;

import '../model/wishlistmodel/wishlistmodel.dart';

import 'package:rapsap/services/logging_service.dart';

class UserService {
  static var client = http.Client();
  static final LoggingService _logger = LoggingService.instance;

  // Helper method to make logged HTTP requests
  static Future<http.Response> _makeLoggedRequest({
    required String method,
    required String url,
    Map<String, String>? headers,
    dynamic body,
  }) async {
    final stopwatch = Stopwatch()..start();

    // Log the request
    _logger.logApiRequest(
      method: method,
      url: url,
      headers: headers,
      body: body is String ? body : json.encode(body),
    );

    http.Response response;
    try {
      switch (method.toUpperCase()) {
        case 'POST':
          if (body is Map<String, String>) {
            response =
                await client.post(Uri.parse(url), body: body, headers: headers);
          } else {
            response =
                await client.post(Uri.parse(url), body: body, headers: headers);
          }
          break;
        case 'GET':
          response = await client.get(Uri.parse(url), headers: headers);
          break;
        default:
          throw UnsupportedError('HTTP method $method not supported');
      }

      stopwatch.stop();

      // Log successful response
      _logger.logApiResponse(
        method: method,
        url: url,
        statusCode: response.statusCode,
        responseBody: response.body.length > 500
            ? '${response.body.substring(0, 500)}... [TRUNCATED]'
            : response.body,
        headers: response.headers,
        responseTime: stopwatch.elapsed,
      );

      return response;
    } catch (e, stackTrace) {
      stopwatch.stop();

      // Log API error
      _logger.logApiError(
        method: method,
        url: url,
        error: e,
        stackTrace: stackTrace,
      );

      rethrow;
    }
  }

  //save token
  static Future saveDeviceToken(params) async {
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    String url = '${baseURL}api/v1/mobile/saveDeviceToken';
    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    var token;
    try {
      final response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: json.encode(params),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        token = jsonMap;
      }
      return token;
    } catch (e) {
      log(e.toString());
    }
    return token;
  }

  static Future checkServiceable(params) async {
    GetStorage storage = GetStorage();
    String jwt = storage.read('jwt') ?? '';

    String url = '${baseURL}api/v1/mobile/checkServiceable';
    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    var pincode;
    try {
      final response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: json.encode(params),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        pincode = jsonMap;
      }
      return pincode;
    } catch (e) {
      log(e.toString());
    }
    return pincode;
  }

  //Email Login
  static Future<UserData?> emailLogin(params) async {
    String url = '${baseURL}api/v1/mobile/loginUser';

    Map<String, String> body = {
      'username': params['email'],
      'password': params['password']
    };

    UserData? userModel;
    try {
      var response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: body,
      );

      if (response.statusCode == 200) {
        var jsonRes = response.body;
        var jsonMap = json.decode(jsonRes);

        userModel = UserData.fromJson(jsonMap);

        if (userModel == null) {
          Get.back();
        }
      }

      return userModel;
    } catch (e) {
      log(e.toString());
    }

    return userModel;
  }

  static Future updateProfile(params) async {
    log('sahal update');
    if (kDebugMode) {
      log(params.toString());
    }

    String url = '${baseURL}api/v1/mobile/editProfile';

    var userModel;
    try {
      log("jwt = ${storage.read('JWT')}");
      final jwt = storage.read('JWT');

      Map<String, String> headers = {
        'Authorization': 'Bearer $jwt',
      };

      final response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: params,
        headers: headers,
      );

      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        if (kDebugMode) {
          log('here-$jsonMap');
        }
        userModel = jsonMap;
      }
      return userModel;
    } catch (e) {
      log(e.toString());
    }
    return userModel;
  }

  // Send OTP with proper logging
  static Future sendOTP(params) async {
    if (kDebugMode) {
      log('Sending OTP with params: $params');
    }

    String url = '${baseURL}api/v1/mobile/sendOtp';

    UserData? userModel;
    try {
      final response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: params, // This should be Map<String, String>
      );

      if (kDebugMode) {
        log('Send OTP Response Status: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        if (kDebugMode) {
          log('Send OTP Response: $jsonMap');
        }

        userModel = UserData.fromJson(jsonMap);
      }

      return userModel;
    } catch (e) {
      if (kDebugMode) {
        log('Send OTP Error: ${e.toString()}');
      }
    }
    return userModel;
  }

  // Verify OTP with proper logging
  static Future verifyOtp(params) async {
    if (kDebugMode) {
      log('Verifying OTP with params: $params');
    }

    String url = '${baseURL}api/v1/mobile/verifyOtp';

    UserData? userModel;
    try {
      final response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: params, // This should be Map<String, String>
      );

      if (kDebugMode) {
        log('Verify OTP Response Status: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        if (kDebugMode) {
          log('Verify OTP Success Response: $jsonMap');
        }

        userModel = UserData.fromJson(jsonMap);
      }

      if (response.statusCode == 403) {
        var jsonRes = response.body;
        var jsonMap = json.decode(jsonRes);
        if (kDebugMode) {
          log('Verify OTP 403 Response: $jsonMap');
        }

        userModel = UserData.fromJson(jsonMap);
      }

      return userModel;
    } catch (e) {
      if (kDebugMode) {
        log('Verify OTP Error: ${e.toString()}');
      }
    }
    return userModel;
  }

  static Future updateWishlist(params) async {
    String url = '${baseURL}api/v1/ecomm/updateWishlist';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    try {
      var response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: json.encode(params),
        headers: headers,
      );

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        Get.to(() => const RootPage());
      } else {
        log('Wishlist update response: $jsonResponse');
        return jsonResponse;
      }
    } catch (e, stackTrace) {
      log('Error in updateWishlist: $e\nStackTrace: $stackTrace');
      return null;
    }
  }

  static Future claimDailyRewards(payload) async {
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    String url = '${baseURL}api/v1/mobile/claimOfferByUser';
    Map<String, String> headers = {'Authorization': 'Bearer $jwt'};
    UserController usertCTRL = Get.find();

    Map<String, String> body = {
      'user_id': usertCTRL.userdata.value.data!.id.toString(),
      'offer_id': payload['offer_id'].toString()
    };

    var currentReward;
    try {
      final response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: body,
        headers: headers,
      );

      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        currentReward = jsonMap;
      }
      return currentReward;
    } catch (e) {
      log(e.toString());
    }
    return currentReward;
  }

  static Future getUserConfig(type) async {
    String url = '${baseURL}api/v1/mobile/userConfig';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    final UserController userController = Get.find();
    try {
      var response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: json.encode({'user_id': userController.userdata.value.data!.id}),
        headers: headers,
      );

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == true) {
        var data = jsonResponse['data'];

        if (data['is_delete'] == 1) {
          return true;
        } else {
          log("User config data: ${data.toString()}");
          if (data['tester'] != 1) {
            if (type == "home") return Get.to(() => const LaunchingSoon());
          }
          return false;
        }
      } else {
        if (type == "home") {
          if (userController.userdata.value.data != null) {
            cleardata();
            return Get.offAll(() => const LoginScreen());
          }
        }
      }
    } catch (e, stackTrace) {
      log('Error in getUserConfig: $e\nStackTrace: $stackTrace');
      return null;
    }
  }

  static Future deleteAccount(params) async {
    String url = '${baseURL}api/v1/mobile/requestAccountDelete';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    try {
      var response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: json.encode(params),
        headers: headers,
      );

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        log('Delete account response: $jsonResponse');
        return jsonResponse;
      }
    } catch (e, stackTrace) {
      log('Error in deleteAccount: $e\nStackTrace: $stackTrace');
      return null;
    }
  }

  static Future<List<Rewards>> getUserRewards1() async {
    String url = '${baseURL}api/v1/mobile/getUserRewards';
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    UserData userData = UserData.fromJson(storage.read('userdata'));
    String userID = userData.data?.id.toString() ?? "";

    Map<String, String> body = {'user_id': userID.toString()};
    Map<String, String> headers = {'Authorization': 'Bearer $jwt'};

    List<Rewards> rewards = [];
    try {
      var response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: body,
        headers: headers,
      );

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);
      if (jsonResponse['success'] == false) {
        Get.to(() => const LoginScreen());
      } else {
        var parsedJSON = jsonResponse['data']['early'];
        rewards = rewardsFromJson(json.encode(parsedJSON));
      }

      return rewards;
    } catch (e) {
      rethrow;
    }
  }

  static Future<WishlistModel?> getWishlist(params) async {
    String url = '${baseURL}api/v1/ecomm/getWishlist';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    WishlistModel? wishlishModel;
    try {
      var response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: json.encode(params),
        headers: headers,
      );

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        Get.to(() => const RootPage());
      } else {
        wishlishModel = WishlistModel.fromJson(jsonResponse);
        return wishlishModel;
      }
    } catch (e, stackTrace) {
      log('Error in getWishlist: $e\nStackTrace: $stackTrace');
      return wishlishModel;
    }
    return wishlishModel;
  }

  //register User
  static Future registerUsr(params) async {
    if (kDebugMode) {
      log('Registering user with params: $params');
    }

    String url = '${baseURL}api/v1/mobile/registerUser';

    UserData? userModel;
    try {
      final response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: params,
      );

      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        if (kDebugMode) {
          log('Register user response: $jsonMap');
        }

        userModel = UserData.fromJson(jsonMap);
      }
      return userModel;
    } catch (e) {
      log(e.toString());
    }
    return userModel;
  }

  //GoogleSignin
  static Future onGoogleSignin(params) async {
    if (kDebugMode) {
      log('Google signin with params: $params');
    }

    String url = '${baseURL}api/v1/mobile/loginWithGoogleIdToken';

    UserData? userModel;
    try {
      final response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: params,
      );

      if (response.statusCode == 200) {
        final jsonRes = response.body;
        final jsonMap = json.decode(jsonRes);
        if (kDebugMode) {
          log('Google signin response: $jsonMap');
        }

        userModel = UserData.fromJson(jsonMap);
      }
      return userModel;
    } catch (e) {
      log(e.toString());
    }
    return userModel;
  }

  static Future<ImageUploadModel?> uploadProfileImage(String filePath) async {
    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';
    final UserController userController = Get.find();
    String url = '${baseURL}api/v1/mobile/uploadUserImage';

    // Log the multipart request
    _logger.logApiRequest(
      method: 'POST',
      url: url,
      headers: {'Authorization': 'Bearer [REDACTED]'},
      body: {
        'user_id': userController.userdata.value.data!.id.toString(),
        'file': 'multipart_file'
      },
    );

    if (kDebugMode) {
      log("Upload image URL: $url");
    }

    var imageRequest = http.MultipartRequest('POST', Uri.parse(url));
    imageRequest.headers.putIfAbsent('Authorization', () => 'Bearer $jwt');
    imageRequest.files
        .add(await http.MultipartFile.fromPath('user_img', filePath));
    imageRequest.fields['user_id'] =
        userController.userdata.value.data!.id.toString();

    final stopwatch = Stopwatch()..start();
    try {
      var streamedResponse = await imageRequest.send();
      var response = await http.Response.fromStream(streamedResponse);
      stopwatch.stop();

      // Log the response
      _logger.logApiResponse(
        method: 'POST',
        url: url,
        statusCode: response.statusCode,
        responseBody: response.body.length > 500
            ? '${response.body.substring(0, 500)}... [TRUNCATED]'
            : response.body,
        headers: response.headers,
        responseTime: stopwatch.elapsed,
      );

      if (kDebugMode) {
        log("Upload image response: ${response.statusCode} ${response.body}");
      }

      if (response.statusCode == 200) {
        final data = imageUploadModelFromJson(response.body);
        final jsonMap = json.decode(response.body);
        log('Upload response: $jsonMap');
        if (data != null) {
          if (data.success == true) {
            return data;
          }
          return null;
        }
      }
    } catch (e, stackTrace) {
      stopwatch.stop();

      _logger.logApiError(
        method: 'POST',
        url: url,
        error: e,
        stackTrace: stackTrace,
      );

      customToast(message: "error:couldn't upload image");
      log(e.toString());
      return null;
    }
    return null;
  }

  static Future<UserData?> getUserDetails(params) async {
    String url = '${baseURL}api/v1/mobile/getUserDetails';

    GetStorage storage = GetStorage();
    String jwt = storage.read('JWT') ?? '';

    Map<String, String> headers = {
      'Authorization': 'Bearer $jwt',
      'Content-type': 'application/json'
    };

    UserData? userModel;
    try {
      var response = await _makeLoggedRequest(
        method: 'POST',
        url: url,
        body: json.encode(params),
        headers: headers,
      );

      var jsonRes = response.body;
      var jsonResponse = json.decode(jsonRes);

      if (jsonResponse['success'] == false) {
        Get.to(() => const LoginScreen());
      } else {
        userModel = UserData.fromJson(jsonResponse);
        return userModel;
      }
    } catch (e, stackTrace) {
      log('Error in getUserDetails: $e\nStackTrace: $stackTrace');
      return userModel;
    }
    return null;
  }
}

// Keep the existing ImageUploadModel classes as they are
ImageUploadModel? imageUploadModelFromJson(String str) =>
    ImageUploadModel.fromJson(json.decode(str));

String imageUploadModelToJson(ImageUploadModel? data) =>
    json.encode(data!.toJson());

class ImageUploadModel {
  ImageUploadModel({
    this.success,
    this.msg,
    this.data,
  });

  bool? success;
  String? msg;
  Data? data;

  factory ImageUploadModel.fromJson(Map<String, dynamic> json) =>
      ImageUploadModel(
        success: json["success"],
        msg: json["msg"],
        data: json["data"] != null ? Data.fromJson(json["data"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "msg": msg,
        "data": data?.toJson(),
      };
}

class Data {
  Data({
    this.imageId,
    this.url,
  });

  int? imageId;
  String? url;

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        imageId: json["image_id"],
        url: json["url"],
      );

  Map<String, dynamic> toJson() => {
        "image_id": imageId,
        "url": url,
      };
}
