import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:rapsap/services/logging_service.dart';

/// HTTP client wrapper that automatically logs all API requests and responses
class HttpLoggingClient extends http.BaseClient {
  final http.Client _inner;
  final LoggingService _logger = LoggingService.instance;

  HttpLoggingClient([http.Client? inner]) : _inner = inner ?? http.Client();

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    final stopwatch = Stopwatch()..start();
    
    // Log the request
    await _logRequest(request);
    
    try {
      final response = await _inner.send(request);
      stopwatch.stop();
      
      // Log the response
      await _logResponse(request, response, stopwatch.elapsed);
      
      return response;
    } catch (error, stackTrace) {
      stopwatch.stop();
      
      // Log the error
      _logError(request, error, stackTrace);
      
      rethrow;
    }
  }

  /// Log HTTP request details
  Future<void> _logRequest(http.BaseRequest request) async {
    try {
      Map<String, dynamic>? body;
      
      // Extract body for different request types
      if (request is http.Request) {
        if (request.body.isNotEmpty) {
          try {
            // Try to parse as JSON first
            body = json.decode(request.body);
          } catch (e) {
            // If not JSON, store as string
            body = {'raw': request.body};
          }
        }
      } else if (request is http.MultipartRequest) {
        body = {
          'fields': request.fields,
          'files': request.files.map((file) => {
            'field': file.field,
            'filename': file.filename,
            'contentType': file.contentType?.toString(),
            'length': file.length,
          }).toList(),
        };
      }

      // Extract query parameters
      Map<String, dynamic>? queryParams;
      if (request.url.hasQuery) {
        queryParams = request.url.queryParameters;
      }

      _logger.logApiRequest(
        method: request.method,
        url: _sanitizeUrl(request.url.toString()),
        headers: _sanitizeHeaders(request.headers),
        body: body,
        queryParams: queryParams,
      );
    } catch (e) {
      // Don't let logging errors break the actual request
      _logger.log(LogLevel.warning, 'Failed to log request: $e');
    }
  }

  /// Log HTTP response details
  Future<void> _logResponse(
    http.BaseRequest request,
    http.StreamedResponse response,
    Duration responseTime,
  ) async {
    try {
      // Read response body
      final responseBytes = await response.stream.toBytes();
      final responseBody = utf8.decode(responseBytes);
      
      dynamic parsedBody;
      try {
        parsedBody = json.decode(responseBody);
      } catch (e) {
        parsedBody = responseBody;
      }

      _logger.logApiResponse(
        method: request.method,
        url: _sanitizeUrl(request.url.toString()),
        statusCode: response.statusCode,
        responseBody: _truncateResponseBody(parsedBody),
        headers: _sanitizeHeaders(response.headers),
        responseTime: responseTime,
      );

      // Create a new response with the consumed body
      final newResponse = http.StreamedResponse(
        Stream.fromIterable([responseBytes]),
        response.statusCode,
        contentLength: responseBytes.length,
        request: response.request,
        headers: response.headers,
        isRedirect: response.isRedirect,
        persistentConnection: response.persistentConnection,
        reasonPhrase: response.reasonPhrase,
      );

      // Replace the original response stream
      response.stream.listen(null).cancel();
    } catch (e) {
      _logger.log(LogLevel.warning, 'Failed to log response: $e');
    }
  }

  /// Log HTTP errors
  void _logError(http.BaseRequest request, dynamic error, StackTrace stackTrace) {
    try {
      int? statusCode;
      
      // Extract status code from different error types
      if (error is HttpException) {
        // Try to extract status code from message
        final match = RegExp(r'(\d{3})').firstMatch(error.message);
        if (match != null) {
          statusCode = int.tryParse(match.group(1)!);
        }
      } else if (error is http.ClientException) {
        // ClientException doesn't have status code
        statusCode = null;
      }

      _logger.logApiError(
        method: request.method,
        url: _sanitizeUrl(request.url.toString()),
        error: error,
        stackTrace: stackTrace,
        statusCode: statusCode,
      );
    } catch (e) {
      _logger.log(LogLevel.warning, 'Failed to log error: $e');
    }
  }

  /// Sanitize URL to remove sensitive information
  String _sanitizeUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final sanitizedQuery = <String, String>{};
      
      // Remove sensitive query parameters
      uri.queryParameters.forEach((key, value) {
        if (_isSensitiveParam(key)) {
          sanitizedQuery[key] = '[REDACTED]';
        } else {
          sanitizedQuery[key] = value;
        }
      });
      
      return uri.replace(queryParameters: sanitizedQuery.isEmpty ? null : sanitizedQuery).toString();
    } catch (e) {
      return url;
    }
  }

  /// Sanitize headers to remove sensitive information
  Map<String, String> _sanitizeHeaders(Map<String, String> headers) {
    final sanitized = <String, String>{};
    
    headers.forEach((key, value) {
      if (_isSensitiveHeader(key)) {
        sanitized[key] = '[REDACTED]';
      } else {
        sanitized[key] = value;
      }
    });
    
    return sanitized;
  }

  /// Check if parameter is sensitive
  bool _isSensitiveParam(String key) {
    final sensitiveParams = [
      'password', 'token', 'secret', 'key', 'auth', 'jwt',
      'api_key', 'access_token', 'refresh_token', 'session'
    ];
    return sensitiveParams.any((param) => key.toLowerCase().contains(param));
  }

  /// Check if header is sensitive
  bool _isSensitiveHeader(String key) {
    final sensitiveHeaders = [
      'authorization', 'cookie', 'set-cookie', 'x-api-key',
      'x-auth-token', 'x-session-token'
    ];
    return sensitiveHeaders.any((header) => key.toLowerCase().contains(header));
  }

  /// Truncate response body if too large
  dynamic _truncateResponseBody(dynamic body) {
    if (body is String && body.length > 1000) {
      return '${body.substring(0, 1000)}... [TRUNCATED]';
    } else if (body is Map || body is List) {
      final jsonString = json.encode(body);
      if (jsonString.length > 1000) {
        return '${jsonString.substring(0, 1000)}... [TRUNCATED]';
      }
    }
    return body;
  }

  @override
  void close() {
    _inner.close();
    super.close();
  }
}

/// Convenience methods for common HTTP operations with logging
class LoggedHttpClient {
  static final HttpLoggingClient _client = HttpLoggingClient();

  static Future<http.Response> get(Uri url, {Map<String, String>? headers}) {
    return _client.get(url, headers: headers);
  }

  static Future<http.Response> post(Uri url, {Map<String, String>? headers, Object? body, Encoding? encoding}) {
    return _client.post(url, headers: headers, body: body, encoding: encoding);
  }

  static Future<http.Response> put(Uri url, {Map<String, String>? headers, Object? body, Encoding? encoding}) {
    return _client.put(url, headers: headers, body: body, encoding: encoding);
  }

  static Future<http.Response> patch(Uri url, {Map<String, String>? headers, Object? body, Encoding? encoding}) {
    return _client.patch(url, headers: headers, body: body, encoding: encoding);
  }

  static Future<http.Response> delete(Uri url, {Map<String, String>? headers, Object? body, Encoding? encoding}) {
    return _client.delete(url, headers: headers, body: body, encoding: encoding);
  }

  static void close() {
    _client.close();
  }
}
