import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

/// Comprehensive logging service for the Flutter application
/// Provides structured logging for API calls, navigation, and user interactions
class LoggingService {
  static LoggingService? _instance;
  static LoggingService get instance => _instance ??= LoggingService._internal();
  
  LoggingService._internal();
  
  late GetStorage _storage;
  bool _isInitialized = false;
  
  // Configuration keys
  static const String _enabledKey = 'logging_enabled';
  static const String _levelKey = 'logging_level';
  static const String _apiLoggingKey = 'api_logging_enabled';
  static const String _navigationLoggingKey = 'navigation_logging_enabled';
  static const String _interactionLoggingKey = 'interaction_logging_enabled';
  
  /// Initialize the logging service
  Future<void> init() async {
    if (_isInitialized) return;
    
    try {
      await GetStorage.init('logging_config');
      _storage = GetStorage('logging_config');
      
      // Set default values if not already set
      if (!_storage.hasData(_enabledKey)) {
        _storage.write(_enabledKey, kDebugMode); // Enable in debug mode by default
      }
      if (!_storage.hasData(_levelKey)) {
        _storage.write(_levelKey, LogLevel.info.index);
      }
      if (!_storage.hasData(_apiLoggingKey)) {
        _storage.write(_apiLoggingKey, true);
      }
      if (!_storage.hasData(_navigationLoggingKey)) {
        _storage.write(_navigationLoggingKey, true);
      }
      if (!_storage.hasData(_interactionLoggingKey)) {
        _storage.write(_interactionLoggingKey, true);
      }
      
      _isInitialized = true;
      _log(LogLevel.info, LogCategory.system, '🚀 Logging service initialized successfully');
    } catch (e) {
      developer.log('Failed to initialize logging service: $e');
    }
  }
  
  // Configuration getters
  bool get isEnabled => _isInitialized && (_storage.read(_enabledKey) ?? false);
  LogLevel get logLevel => LogLevel.values[_storage.read(_levelKey) ?? LogLevel.info.index];
  bool get isApiLoggingEnabled => _storage.read(_apiLoggingKey) ?? true;
  bool get isNavigationLoggingEnabled => _storage.read(_navigationLoggingKey) ?? true;
  bool get isInteractionLoggingEnabled => _storage.read(_interactionLoggingKey) ?? true;
  
  // Configuration setters
  void setEnabled(bool enabled) => _storage.write(_enabledKey, enabled);
  void setLogLevel(LogLevel level) => _storage.write(_levelKey, level.index);
  void setApiLoggingEnabled(bool enabled) => _storage.write(_apiLoggingKey, enabled);
  void setNavigationLoggingEnabled(bool enabled) => _storage.write(_navigationLoggingKey, enabled);
  void setInteractionLoggingEnabled(bool enabled) => _storage.write(_interactionLoggingKey, enabled);
  
  /// Log API request
  void logApiRequest({
    required String method,
    required String url,
    Map<String, dynamic>? headers,
    dynamic body,
    Map<String, dynamic>? queryParams,
  }) {
    if (!isEnabled || !isApiLoggingEnabled) return;
    
    final emoji = _getMethodEmoji(method);
    final message = '$emoji API Request: $method $url';
    final details = {
      'method': method,
      'url': url,
      'headers': headers,
      'body': body,
      'queryParams': queryParams,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    _log(LogLevel.info, LogCategory.api, message, details);
  }
  
  /// Log API response
  void logApiResponse({
    required String method,
    required String url,
    required int statusCode,
    dynamic responseBody,
    Map<String, String>? headers,
    required Duration responseTime,
  }) {
    if (!isEnabled || !isApiLoggingEnabled) return;
    
    final emoji = statusCode >= 200 && statusCode < 300 ? '✅' : '⚠️';
    final message = '$emoji API Response: $method $url - $statusCode (${responseTime.inMilliseconds}ms)';
    final details = {
      'method': method,
      'url': url,
      'statusCode': statusCode,
      'responseBody': responseBody,
      'headers': headers,
      'responseTime': '${responseTime.inMilliseconds}ms',
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    final level = statusCode >= 400 ? LogLevel.warning : LogLevel.info;
    _log(level, LogCategory.api, message, details);
  }
  
  /// Log API error
  void logApiError({
    required String method,
    required String url,
    required dynamic error,
    StackTrace? stackTrace,
    int? statusCode,
  }) {
    if (!isEnabled || !isApiLoggingEnabled) return;
    
    final message = '❌ API Error: $method $url - $error';
    final details = {
      'method': method,
      'url': url,
      'error': error.toString(),
      'statusCode': statusCode,
      'stackTrace': stackTrace?.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    _log(LogLevel.error, LogCategory.api, message, details);
  }
  
  /// Log navigation event
  void logNavigation({
    required String action,
    required String from,
    required String to,
    Map<String, dynamic>? arguments,
  }) {
    if (!isEnabled || !isNavigationLoggingEnabled) return;
    
    final emoji = _getNavigationEmoji(action);
    final message = '$emoji Navigation: $action from $from to $to';
    final details = {
      'action': action,
      'from': from,
      'to': to,
      'arguments': arguments,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    _log(LogLevel.info, LogCategory.navigation, message, details);
  }
  
  /// Log user interaction
  void logInteraction({
    required String type,
    required String element,
    String? screen,
    Map<String, dynamic>? data,
  }) {
    if (!isEnabled || !isInteractionLoggingEnabled) return;
    
    final emoji = _getInteractionEmoji(type);
    final message = '$emoji User Interaction: $type on $element${screen != null ? ' in $screen' : ''}';
    final details = {
      'type': type,
      'element': element,
      'screen': screen,
      'data': data,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    _log(LogLevel.info, LogCategory.interaction, message, details);
  }
  
  /// Generic logging method
  void log(LogLevel level, String message, [Map<String, dynamic>? details]) {
    if (!isEnabled) return;
    _log(level, LogCategory.general, message, details);
  }
  
  /// Internal logging method
  void _log(LogLevel level, LogCategory category, String message, [Map<String, dynamic>? details]) {
    if (!isEnabled || level.index < logLevel.index) return;
    
    final timestamp = DateFormat('HH:mm:ss.SSS').format(DateTime.now());
    final levelStr = level.name.toUpperCase().padRight(7);
    final categoryStr = category.name.toUpperCase().padRight(11);
    
    final logMessage = '[$timestamp] [$levelStr] [$categoryStr] $message';
    
    // Log to console
    developer.log(logMessage);
    
    // Log details if provided
    if (details != null && details.isNotEmpty) {
      try {
        final prettyDetails = const JsonEncoder.withIndent('  ').convert(details);
        developer.log('Details:\n$prettyDetails');
      } catch (e) {
        developer.log('Details: $details');
      }
    }
  }
  
  /// Get emoji for HTTP method
  String _getMethodEmoji(String method) {
    switch (method.toUpperCase()) {
      case 'GET': return '🔍';
      case 'POST': return '📝';
      case 'PUT': return '✏️';
      case 'PATCH': return '🔧';
      case 'DELETE': return '🗑️';
      default: return '📡';
    }
  }
  
  /// Get emoji for navigation action
  String _getNavigationEmoji(String action) {
    switch (action.toLowerCase()) {
      case 'push':
      case 'to': return '➡️';
      case 'pop':
      case 'back': return '⬅️';
      case 'replace': return '🔄';
      case 'offAll': return '🏠';
      default: return '🧭';
    }
  }
  
  /// Get emoji for interaction type
  String _getInteractionEmoji(String type) {
    switch (type.toLowerCase()) {
      case 'tap':
      case 'click': return '👆';
      case 'form_submit': return '📋';
      case 'swipe': return '👉';
      case 'scroll': return '📜';
      case 'input': return '⌨️';
      case 'search': return '🔍';
      default: return '🎯';
    }
  }
}

/// Log levels
enum LogLevel {
  debug,
  info,
  warning,
  error,
}

/// Log categories
enum LogCategory {
  general,
  api,
  navigation,
  interaction,
  system,
}
