import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rapsap/services/logging_service.dart';

/// Navigation observer that logs all navigation events
class NavigationLogger extends NavigatorObserver {
  final LoggingService _logger = LoggingService.instance;
  final List<String> _routeStack = [];

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    
    final routeName = _getRouteName(route);
    final previousRouteName = previousRoute != null ? _getRouteName(previousRoute) : 'none';
    
    _routeStack.add(routeName);
    
    _logger.logNavigation(
      action: 'push',
      from: previousRouteName,
      to: routeName,
      arguments: _getRouteArguments(route),
    );
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    
    final routeName = _getRouteName(route);
    final previousRouteName = previousRoute != null ? _getRouteName(previousRoute) : 'none';
    
    if (_routeStack.isNotEmpty) {
      _routeStack.removeLast();
    }
    
    _logger.logNavigation(
      action: 'pop',
      from: routeName,
      to: previousRouteName,
      arguments: _getRouteArguments(route),
    );
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    
    final newRouteName = newRoute != null ? _getRouteName(newRoute) : 'unknown';
    final oldRouteName = oldRoute != null ? _getRouteName(oldRoute) : 'unknown';
    
    if (_routeStack.isNotEmpty && oldRoute != null) {
      _routeStack.removeLast();
    }
    if (newRoute != null) {
      _routeStack.add(newRouteName);
    }
    
    _logger.logNavigation(
      action: 'replace',
      from: oldRouteName,
      to: newRouteName,
      arguments: newRoute != null ? _getRouteArguments(newRoute) : null,
    );
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didRemove(route, previousRoute);
    
    final routeName = _getRouteName(route);
    final previousRouteName = previousRoute != null ? _getRouteName(previousRoute) : 'none';
    
    _routeStack.removeWhere((r) => r == routeName);
    
    _logger.logNavigation(
      action: 'remove',
      from: routeName,
      to: previousRouteName,
      arguments: _getRouteArguments(route),
    );
  }

  /// Extract route name from route
  String _getRouteName(Route<dynamic> route) {
    if (route.settings.name != null) {
      return route.settings.name!;
    }
    
    // Try to extract class name from route
    final routeString = route.toString();
    final match = RegExp(r'(\w+)Route').firstMatch(routeString);
    if (match != null) {
      return match.group(1)!;
    }
    
    // Fallback to route type
    return route.runtimeType.toString();
  }

  /// Extract route arguments
  Map<String, dynamic>? _getRouteArguments(Route<dynamic> route) {
    final arguments = route.settings.arguments;
    if (arguments == null) return null;
    
    try {
      if (arguments is Map<String, dynamic>) {
        return arguments;
      } else {
        return {'arguments': arguments.toString()};
      }
    } catch (e) {
      return {'arguments': 'Failed to serialize: $e'};
    }
  }

  /// Get current route stack
  List<String> get routeStack => List.unmodifiable(_routeStack);
}

/// Extended GetX navigation methods with logging
class LoggedNavigation {
  static final LoggingService _logger = LoggingService.instance;

  /// Navigate to a new page with logging
  static Future<T?>? to<T>(
    Widget page, {
    bool? opaque,
    Transition? transition,
    Duration? duration,
    int? id,
    String? routeName,
    bool fullscreenDialog = false,
    dynamic arguments,
    Bindings? binding,
    bool preventDuplicates = true,
    bool? popGesture,
    double Function(BuildContext context)? gestureWidth,
  }) {
    final currentRoute = Get.currentRoute;
    final targetRoute = routeName ?? page.runtimeType.toString();
    
    _logger.logNavigation(
      action: 'to',
      from: currentRoute,
      to: targetRoute,
      arguments: arguments != null ? {'arguments': arguments.toString()} : null,
    );
    
    return Get.to<T>(
      page,
      opaque: opaque,
      transition: transition,
      duration: duration,
      id: id,
      routeName: routeName,
      fullscreenDialog: fullscreenDialog,
      arguments: arguments,
      binding: binding,
      preventDuplicates: preventDuplicates,
      popGesture: popGesture,
      gestureWidth: gestureWidth,
    );
  }

  /// Navigate back with logging
  static void back<T>({
    T? result,
    bool closeOverlays = false,
    bool canPop = true,
    int? id,
  }) {
    final currentRoute = Get.currentRoute;

    _logger.logNavigation(
      action: 'back',
      from: currentRoute,
      to: 'previous',
      arguments: result != null ? {'result': result.toString()} : null,
    );

    Get.back<T>(
      result: result,
      closeOverlays: closeOverlays,
      canPop: canPop,
      id: id,
    );
  }

  /// Navigate off all with logging
  static Future<T?>? offAll<T>(
    Widget page, {
    RoutePredicate? predicate,
    bool opaque = true,
    bool? popGesture,
    int? id,
    String? routeName,
    dynamic arguments,
    Bindings? binding,
    bool fullscreenDialog = false,
    Transition? transition,
    Duration? duration,
  }) {
    final currentRoute = Get.currentRoute;
    final targetRoute = routeName ?? page.runtimeType.toString();
    
    _logger.logNavigation(
      action: 'offAll',
      from: currentRoute,
      to: targetRoute,
      arguments: arguments != null ? {'arguments': arguments.toString()} : null,
    );
    
    return Get.offAll<T>(
      page,
      predicate: predicate,
      opaque: opaque,
      popGesture: popGesture,
      id: id,
      routeName: routeName,
      arguments: arguments,
      binding: binding,
      fullscreenDialog: fullscreenDialog,
      transition: transition,
      duration: duration,
    );
  }

  /// Navigate off with logging
  static Future<T?>? off<T>(
    Widget page, {
    bool opaque = true,
    Transition? transition,
    Duration? duration,
    bool? popGesture,
    int? id,
    String? routeName,
    dynamic arguments,
    Bindings? binding,
    bool fullscreenDialog = false,
  }) {
    final currentRoute = Get.currentRoute;
    final targetRoute = routeName ?? page.runtimeType.toString();
    
    _logger.logNavigation(
      action: 'off',
      from: currentRoute,
      to: targetRoute,
      arguments: arguments != null ? {'arguments': arguments.toString()} : null,
    );
    
    return Get.off<T>(
      page,
      opaque: opaque,
      transition: transition,
      duration: duration,
      popGesture: popGesture,
      id: id,
      routeName: routeName,
      arguments: arguments,
      binding: binding,
      fullscreenDialog: fullscreenDialog,
    );
  }

  /// Navigate until with logging
  static void until(RoutePredicate predicate, {int? id}) {
    final currentRoute = Get.currentRoute;

    _logger.logNavigation(
      action: 'until',
      from: currentRoute,
      to: 'predicate_match',
      arguments: null,
    );

    Get.until(predicate, id: id);
  }

  /// Close current snackbar, dialog, bottomsheet, or anything
  static void close([int times = 1]) {
    final currentRoute = Get.currentRoute;

    _logger.logNavigation(
      action: 'close',
      from: currentRoute,
      to: 'closed',
      arguments: {'times': times},
    );

    Get.close(times);
  }
}
