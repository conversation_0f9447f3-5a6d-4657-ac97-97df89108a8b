import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';
import 'package:rapsap/services/logging_service.dart';

/// Configuration management for the logging system
class LoggingConfig {
  static LoggingConfig? _instance;
  static LoggingConfig get instance => _instance ??= LoggingConfig._internal();
  
  LoggingConfig._internal();
  
  late GetStorage _storage;
  bool _isInitialized = false;
  
  // Configuration keys
  static const String _configPrefix = 'logging_';
  static const String _enabledKey = '${_configPrefix}enabled';
  static const String _levelKey = '${_configPrefix}level';
  static const String _apiLoggingKey = '${_configPrefix}api_enabled';
  static const String _navigationLoggingKey = '${_configPrefix}navigation_enabled';
  static const String _interactionLoggingKey = '${_configPrefix}interaction_enabled';
  static const String _consoleOutputKey = '${_configPrefix}console_output';
  static const String _fileOutputKey = '${_configPrefix}file_output';
  static const String _maxLogFileSizeKey = '${_configPrefix}max_file_size';
  static const String _logRetentionDaysKey = '${_configPrefix}retention_days';
  static const String _sensitiveDataLoggingKey = '${_configPrefix}sensitive_data';
  static const String _performanceLoggingKey = '${_configPrefix}performance';
  
  /// Initialize the configuration system
  Future<void> init() async {
    if (_isInitialized) return;
    
    try {
      await GetStorage.init('logging_config');
      _storage = GetStorage('logging_config');
      
      // Set default values if not already set
      _setDefaultValues();
      
      _isInitialized = true;
    } catch (e) {
      debugPrint('Failed to initialize logging config: $e');
    }
  }
  
  /// Set default configuration values
  void _setDefaultValues() {
    final defaults = {
      _enabledKey: kDebugMode, // Enable in debug mode by default
      _levelKey: LogLevel.info.index,
      _apiLoggingKey: true,
      _navigationLoggingKey: true,
      _interactionLoggingKey: true,
      _consoleOutputKey: true,
      _fileOutputKey: false, // Disabled by default for performance
      _maxLogFileSizeKey: 10 * 1024 * 1024, // 10MB
      _logRetentionDaysKey: 7, // Keep logs for 7 days
      _sensitiveDataLoggingKey: false, // Don't log sensitive data by default
      _performanceLoggingKey: kDebugMode, // Enable performance logging in debug mode
    };
    
    defaults.forEach((key, value) {
      if (!_storage.hasData(key)) {
        _storage.write(key, value);
      }
    });
  }
  
  // Getters for configuration values
  bool get isEnabled => _isInitialized && (_storage.read(_enabledKey) ?? false);
  LogLevel get logLevel => LogLevel.values[_storage.read(_levelKey) ?? LogLevel.info.index];
  bool get isApiLoggingEnabled => _storage.read(_apiLoggingKey) ?? true;
  bool get isNavigationLoggingEnabled => _storage.read(_navigationLoggingKey) ?? true;
  bool get isInteractionLoggingEnabled => _storage.read(_interactionLoggingKey) ?? true;
  bool get isConsoleOutputEnabled => _storage.read(_consoleOutputKey) ?? true;
  bool get isFileOutputEnabled => _storage.read(_fileOutputKey) ?? false;
  int get maxLogFileSize => _storage.read(_maxLogFileSizeKey) ?? (10 * 1024 * 1024);
  int get logRetentionDays => _storage.read(_logRetentionDaysKey) ?? 7;
  bool get isSensitiveDataLoggingEnabled => _storage.read(_sensitiveDataLoggingKey) ?? false;
  bool get isPerformanceLoggingEnabled => _storage.read(_performanceLoggingKey) ?? kDebugMode;
  
  // Setters for configuration values
  void setEnabled(bool enabled) {
    _storage.write(_enabledKey, enabled);
    LoggingService.instance.setEnabled(enabled);
  }
  
  void setLogLevel(LogLevel level) {
    _storage.write(_levelKey, level.index);
    LoggingService.instance.setLogLevel(level);
  }
  
  void setApiLoggingEnabled(bool enabled) {
    _storage.write(_apiLoggingKey, enabled);
    LoggingService.instance.setApiLoggingEnabled(enabled);
  }
  
  void setNavigationLoggingEnabled(bool enabled) {
    _storage.write(_navigationLoggingKey, enabled);
    LoggingService.instance.setNavigationLoggingEnabled(enabled);
  }
  
  void setInteractionLoggingEnabled(bool enabled) {
    _storage.write(_interactionLoggingKey, enabled);
    LoggingService.instance.setInteractionLoggingEnabled(enabled);
  }
  
  void setConsoleOutputEnabled(bool enabled) {
    _storage.write(_consoleOutputKey, enabled);
  }
  
  void setFileOutputEnabled(bool enabled) {
    _storage.write(_fileOutputKey, enabled);
  }
  
  void setMaxLogFileSize(int sizeInBytes) {
    _storage.write(_maxLogFileSizeKey, sizeInBytes);
  }
  
  void setLogRetentionDays(int days) {
    _storage.write(_logRetentionDaysKey, days);
  }
  
  void setSensitiveDataLoggingEnabled(bool enabled) {
    _storage.write(_sensitiveDataLoggingKey, enabled);
  }
  
  void setPerformanceLoggingEnabled(bool enabled) {
    _storage.write(_performanceLoggingKey, enabled);
  }
  
  /// Get all configuration as a map
  Map<String, dynamic> getAllConfig() {
    return {
      'enabled': isEnabled,
      'logLevel': logLevel.name,
      'apiLogging': isApiLoggingEnabled,
      'navigationLogging': isNavigationLoggingEnabled,
      'interactionLogging': isInteractionLoggingEnabled,
      'consoleOutput': isConsoleOutputEnabled,
      'fileOutput': isFileOutputEnabled,
      'maxLogFileSize': maxLogFileSize,
      'logRetentionDays': logRetentionDays,
      'sensitiveDataLogging': isSensitiveDataLoggingEnabled,
      'performanceLogging': isPerformanceLoggingEnabled,
    };
  }
  
  /// Reset all configuration to defaults
  void resetToDefaults() {
    _storage.erase();
    _setDefaultValues();
    
    // Update logging service with new defaults
    final loggingService = LoggingService.instance;
    loggingService.setEnabled(isEnabled);
    loggingService.setLogLevel(logLevel);
    loggingService.setApiLoggingEnabled(isApiLoggingEnabled);
    loggingService.setNavigationLoggingEnabled(isNavigationLoggingEnabled);
    loggingService.setInteractionLoggingEnabled(isInteractionLoggingEnabled);
  }
  
  /// Export configuration as JSON string
  String exportConfig() {
    return _storage.getValues().toString();
  }
  
  /// Import configuration from JSON string
  bool importConfig(Map<String, dynamic> config) {
    try {
      config.forEach((key, value) {
        if (key.startsWith(_configPrefix)) {
          _storage.write(key, value);
        }
      });
      
      // Update logging service with imported config
      final loggingService = LoggingService.instance;
      loggingService.setEnabled(isEnabled);
      loggingService.setLogLevel(logLevel);
      loggingService.setApiLoggingEnabled(isApiLoggingEnabled);
      loggingService.setNavigationLoggingEnabled(isNavigationLoggingEnabled);
      loggingService.setInteractionLoggingEnabled(isInteractionLoggingEnabled);
      
      return true;
    } catch (e) {
      debugPrint('Failed to import logging config: $e');
      return false;
    }
  }
  
  /// Quick configuration presets
  void applyPreset(LoggingPreset preset) {
    switch (preset) {
      case LoggingPreset.disabled:
        setEnabled(false);
        break;
        
      case LoggingPreset.minimal:
        setEnabled(true);
        setLogLevel(LogLevel.warning);
        setApiLoggingEnabled(false);
        setNavigationLoggingEnabled(false);
        setInteractionLoggingEnabled(false);
        setPerformanceLoggingEnabled(false);
        break;
        
      case LoggingPreset.development:
        setEnabled(true);
        setLogLevel(LogLevel.debug);
        setApiLoggingEnabled(true);
        setNavigationLoggingEnabled(true);
        setInteractionLoggingEnabled(true);
        setPerformanceLoggingEnabled(true);
        setConsoleOutputEnabled(true);
        setFileOutputEnabled(false);
        break;
        
      case LoggingPreset.production:
        setEnabled(true);
        setLogLevel(LogLevel.error);
        setApiLoggingEnabled(true);
        setNavigationLoggingEnabled(false);
        setInteractionLoggingEnabled(false);
        setPerformanceLoggingEnabled(false);
        setConsoleOutputEnabled(false);
        setFileOutputEnabled(true);
        setSensitiveDataLoggingEnabled(false);
        break;
        
      case LoggingPreset.debugging:
        setEnabled(true);
        setLogLevel(LogLevel.debug);
        setApiLoggingEnabled(true);
        setNavigationLoggingEnabled(true);
        setInteractionLoggingEnabled(true);
        setPerformanceLoggingEnabled(true);
        setConsoleOutputEnabled(true);
        setFileOutputEnabled(true);
        setSensitiveDataLoggingEnabled(true);
        break;
    }
  }
  
  /// Validate configuration
  bool validateConfig() {
    try {
      // Check if all required keys exist
      final requiredKeys = [
        _enabledKey, _levelKey, _apiLoggingKey, _navigationLoggingKey,
        _interactionLoggingKey, _consoleOutputKey, _fileOutputKey
      ];
      
      for (final key in requiredKeys) {
        if (!_storage.hasData(key)) {
          return false;
        }
      }
      
      // Validate log level
      final levelIndex = _storage.read(_levelKey);
      if (levelIndex < 0 || levelIndex >= LogLevel.values.length) {
        return false;
      }
      
      // Validate file size
      final maxSize = _storage.read(_maxLogFileSizeKey);
      if (maxSize <= 0) {
        return false;
      }
      
      // Validate retention days
      final retentionDays = _storage.read(_logRetentionDaysKey);
      if (retentionDays <= 0) {
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }
}

/// Predefined logging configuration presets
enum LoggingPreset {
  disabled,
  minimal,
  development,
  production,
  debugging,
}
