import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:rapsap/model/SubcategoryModel/sub_category_model/datum.dart';
import 'package:rapsap/view/widgets/commons.dart';
import 'package:get/get.dart';

import '../model/category_product_model/category_products_model/datum.dart';
import '../services/category_service.dart';

class CategoryController extends GetxController {
  List<CategoryProducts?> categoryproductmodel = [];
  List<SubCategoryData?> subcategorymodel = [];
  RxString name = "".obs;

  bool exitfuncion = false;
  bool loading = false;
  bool subcategoryloading = true;
  bool nextpageloading = true;

  int selectedcategoryid = 0;

  int selectedindex = 0;
  int page = 1;

  RxInt categorytotal = 0.obs;

  Future<List<CategoryProducts?>> getProductsByCategory(
      {required int categoryId,
      String? type,
      int page = 0,
      String? subcategoryid,
      String? filter}) async {
    final res = await CategoryService.getProductsByCategory(
        categoryId: categoryId,
        type: type,
        page: page,
        subcategoryid: subcategoryid,
        filter: filter);
    if (kDebugMode) {
      print(res);
    }
    log("called");

    if (res != null) {
      if (res.success == true) {
        log("        res.data.length  ${res.data!.length}");

        if (type == "similar") {
          categoryproductmodel = res.data!;
          return categoryproductmodel;
        }
        if (res.data!.isNotEmpty) {
          if (subcategorymodel[selectedindex]!.subCategoryId ==
              res.data!.first.subCategoryId) {
            if (page == 0) {
              categoryproductmodel = res.data!;
            } else {
              categoryproductmodel = categoryproductmodel + res.data!;
            }

            if (subcategorymodel[selectedindex]!.subCategoryId ==
                res.data!.first.subCategoryId) {
              loading = false;
              update();
            }
          }
        } else {
          loading = false;
        }
        nextpageloading = false;
        update();

        categorytotal.value = res.meta!.total!;

        update();
      }
    }

    if (kDebugMode) {
      print(categoryproductmodel.toList());
    }
    return categoryproductmodel;
  }

  Future<List<SubCategoryData?>> getSubcategoryList({
    required int categoryId,
  }) async {
    final res = await CategoryService.getSUbCategoryList(
      categoryId: categoryId,
    );
    // print(subcategoryid);
    log("subcategory called");

    if (res != null) {
      if (res.success == true) {
        if (selectedcategoryid == categoryId) {
          subcategorymodel = res.data!;
          subcategoryloading = false;
        }

        update();
        // categorytotal = res.meta!.total!;
      }
    }

    return subcategorymodel;
  }

  // void dispose() {
  //   scrollController.dispose();

  //   super.dispose();
  // }
}
