import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:rapsap/model/order/order_list_model/order_list_model/order_list_model.dart';
import 'package:rapsap/model/order/orderdetailmodel/order_detail_model/order_detail_model.dart';
import 'package:rapsap/services/orderservices.dart';

class OrderController extends GetxController {
  var orderListModel = OrderListModel().obs;
  var orderDetailModel = OrderDetailModel().obs;
  RxBool buttonloading = false.obs;

  Future<OrderListModel?> getOrders() async {
    if (kDebugMode) {
      print("get order called ");
    }
    final result = await OrderServices.getOrder();
    if (result != null) {
      if (result.success == true) {
        orderListModel.value = result;
      }
      if (kDebugMode) {
        log(result.toJson());
      }
    }

    return result;
  }

  Future<OrderDetailModel?> getOrderbyId(orderid) async {
    if (kDebugMode) {
      print("get order called ");
    }
    final result = await OrderServices.getOrderById(orderid);
    if (result != null) {
      if (result.success == true) {
        orderDetailModel.value = result;
      }
      if (kDebugMode) {
        log(result.toJson());
      }
    }

    return result;
  }
}
