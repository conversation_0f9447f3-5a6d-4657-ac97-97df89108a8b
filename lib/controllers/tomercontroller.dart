import 'dart:async';
import 'package:get/get.dart';

class CountDownTimerState extends GetxController {
  var sCount = 30;

  late Timer _timer;
  @override
  void onInit() {
    stateTimerStart();
    // TODO: implement onInit
    super.onInit();
  }

  void stateTimerStart() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (sCount > 0) {
        sCount--;
        update();
      } else {
        _timer.cancel();
      }
    });
  }
}
