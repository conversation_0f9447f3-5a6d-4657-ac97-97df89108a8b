import 'dart:developer';

import 'package:get/get.dart';
import 'package:rapsap/model/productmodel/products/products.dart';
import 'package:rapsap/model/product_review_model/product_review_model.dart';
import 'package:rapsap/services/product_service.dart';

class ProductViewController extends GetxController {
  int _carouselIndex = 0;
  RxInt variantindex = 0.obs;
  bool favourite = false;
  double currentdiscount = 0;

  List<ProductReviewModel> reviewList = [];

  bool _productDetailsTileExpanded = false;

  int get carouselIndex => _carouselIndex;
  set carouselIndex(int val) {
    _carouselIndex = val;
    update();
  }

  bool get productDetailsTileExpanded => _productDetailsTileExpanded;
  set productDetailsTileExpanded(bool val) {
    _productDetailsTileExpanded = val;
    update();
  }

  Future<ProductReviewModel?> getProductReviews(
      int productId, int rating) async {
    log("function called ");
    final result = await ProductService.getProductReviews(
      productId: productId,
      rating: rating,
    );
    return result;
  }

  Future<Products?> getproducts(int productId) async {
    log("function called ");
    final result = await ProductService.getProductbyID(
      productId: productId,
    );

    log("res=$result");

    return result;
  }

  int getProductImagesCount(int productImageLength) {
    update();
    return productImageLength;
  }
}
