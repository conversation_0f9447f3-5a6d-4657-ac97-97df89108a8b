import 'package:flutter/foundation.dart';
import 'package:rapsap/controllers/user_controller.dart';
import 'package:rapsap/model/wishlistmodel/wishlistmodel.dart';
import 'package:get/get.dart';

import '../main.dart';
import '../services/userservices.dart';

class Wishlistcontroller extends GetxController {
  WishlistModel? wishlistModel;
  List<WislistItems>? wislistItems;
  RxBool futuredelay = false.obs;
  getwishlistitems() async {
    final UserController userController = Get.find();
    final list = await UserService.getWishlist({
      "user_id": userController.userdata.value.data!.id.toString(),
      "store_id": storage.read('storeID') ?? 0.toString(),
    });
    if (list != null) {
      if (list.success!) {
        wishlistModel = list;
        wislistItems = list.data;
      }
    }

    if (kDebugMode) {
      print(list);
    }
  }
}
