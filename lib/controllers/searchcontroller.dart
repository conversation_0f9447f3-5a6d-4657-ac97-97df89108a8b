import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rapsap/model/searchModel/search_model/search_model.dart';
import 'package:rapsap/services/searchservice.dart';

class SearchController extends GetxController {
  Rx<SearchModel> searchresults = SearchModel().obs;

  RxList searchlist = [].obs;
  RxInt searchtotal = 0.obs;

  RxBool searchLoading = false.obs;
  final Rx<TextEditingController> textEditingController =
      TextEditingController().obs;

  bool nextpageloading = false;

  Future<SearchModel?> getproducts({
    query,
    int page = 0,
  }) async {
    log("function called ");
    final result = await SearchService.getProducts(query, page);
    if (result != null) {
      if (page == 0) {
        searchresults.value = result;
      } else {
        searchresults.value.data = (searchresults.value.data! + result.data!);
        nextpageloading = false;
        update();
      }

      searchtotal.value = result.meta!.total!;

      log(result.toJson());
    }

    return result;
  }
}
