import 'dart:developer';
import 'package:intl_phone_field/phone_number.dart';
import 'package:rapsap/view/screens/AddressPage/addaddresspage.dart';
import 'package:carousel_slider/carousel_controller.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:rapsap/model/user_model/usermodel.dart';
import 'package:rapsap/view/screens/mapscreen/mappage.dart';
import 'package:rapsap/view/screens/root_page/root_page.dart';
import '../main.dart';
import '../services/firebaseservices.dart';
import '../services/userservices.dart';
import '../view/screens/login/personaldetailscreen.dart';
import 'package:rapsap/services/logging_service.dart';
import 'package:rapsap/services/interaction_logger.dart';

class UserController extends GetxController {
  RxBool showpassword = true.obs;
  String error = "";
  RxBool serviceloading = false.obs;
  RxBool tester = false.obs;

  String currentpin = "";
  bool loading = false;
  RxBool buttonloading = false.obs;

  RxBool numberSelected = true.obs;

  Rx<PhoneNumber?> selectedPhoneNumber = Rx<PhoneNumber?>(null);

  final Rx<TextEditingController> phoneController = TextEditingController().obs;
  Rx<UserData> userdata = const UserData().obs;
  RxBool loginstatus = false.obs;
  var locationaddress = const Placemark().obs;
  var locationpoint = {}.obs;
  RxString image = ''.obs;

  static final LoggingService _logger = LoggingService.instance;

  // Mobileno Get
  void onPhoneNumberChanged(PhoneNumber phoneNumber,
    CarouselController controller, FocusNode otpctrlr) {
  selectedPhoneNumber.value = phoneNumber;

  // Log phone number input interaction
  InteractionLogger.logTextInput(
    fieldName: 'phone_number',
    textLength: phoneNumber.number.length,
    inputType: 'phone',
  );

  if (kDebugMode) {
    log('Complete Number: ${phoneNumber.completeNumber}');
    log('Country Code: ${phoneNumber.countryCode}');
    log('Number: ${phoneNumber.number}');
  }

  // Update the phone controller with the number (without country code for display)
  phoneController.value.text = phoneNumber.number;

  // Auto-trigger OTP when number is complete (10 digits for India)
  if (phoneNumber.number.length >= 10) {
    numberSelected.value = true;
    FocusManager.instance.primaryFocus?.unfocus();

    // Log OTP request interaction
    InteractionLogger.logButtonTap(
      buttonName: 'send_otp_auto',
      additionalData: {
        'phone_number_length': phoneNumber.number.length,
        'country_code': phoneNumber.countryCode,
      },
    );

    // Send OTP with complete number (including country code)
    // Make sure to pass the data as Map<String, String>
    Map<String, String> otpData = {
      'mobile': phoneNumber.completeNumber,
    };
    sendOtp(otpData);

    // Navigate to OTP page
    controller.animateToPage(1);
    otpctrlr.requestFocus();
    update();
  } else {
    numberSelected.value = false;
  }
}

  // Legacy method - kept for backward compatibility but updated to work with new system
  void getmobilenumber(CarouselController controller, FocusNode otpctrlr) {
    // This method is now obsolete with intl_phone_field
    if (kDebugMode) {
      log('getmobilenumber called - this method is obsolete with intl_phone_field');
    }
  }

  // Method to validate phone number
  bool validatePhoneNumber(PhoneNumber? phoneNumber) {
    if (phoneNumber == null) return false;
    return phoneNumber.number.isNotEmpty && phoneNumber.number.length >= 10;
  }

  // Method to get formatted phone number (with country code)
  String getFormattedPhoneNumber() {
    if (selectedPhoneNumber.value != null) {
      return selectedPhoneNumber.value!.completeNumber;
    }
    // Fallback for backward compatibility
    return phoneController.value.text.isNotEmpty
        ? '+91${phoneController.value.text}'
        : '';
  }

  // Method to get just the number without country code
  String getPhoneNumberOnly() {
    return selectedPhoneNumber.value?.number ?? phoneController.value.text;
  }

  // Method to get country code
  String getCountryCode() {
    return selectedPhoneNumber.value?.countryCode ?? '+91';
  }

  // Register User
  void registerUser(params) async {
    log("Register user params: $params");

    // Log registration attempt
    InteractionLogger.logFormSubmit(
      formName: 'user_registration',
      formData: params,
    );

    var user = await UserService.registerUsr(params);
    if (kDebugMode) {
      log('Registration response: $user');
    }

    if (user?.success == true) {
      // Log successful registration
      InteractionLogger.logFormSubmit(
        formName: 'user_registration',
        success: true,
      );

      await storage.write('JWT', user.data?.token);
      log(storage.read('JWT'));

      storage.write('userdata', UserData(data: user.data).toJson());
      userdata.value = UserData(data: user.data);
      loginstatus.value = true;
      storage.write('loginstatus', loginstatus.value);

      String? token = await FirebaseMessaging.instance.getToken();
      var req = {
        'user_id': userdata.value.data!.id.toString(),
        'user_firebase_token': token,
      };
      await UserService.saveDeviceToken(req);

      await FirebaseService.firebaseAnalytics!.logEvent(
        name: 'login_user',
        parameters: {
          'name': userdata.value.data!.firstName!,
          'userid': userdata.value.data!.id!,
        },
      );
      await FirebaseService.firebaseAnalytics!.logLogin(loginMethod: "email");

      if (storage.read('locationaddress') != null) {
        locationaddress.value =
            Placemark.fromMap(storage.read('locationaddress'));
      }

      if (userdata.value.data?.deleted == 1) {
        Future.delayed(const Duration(milliseconds: 1000),
            (() => Get.to(() => const RootPage(data: true))));
        return;
      }

      Future.delayed(const Duration(milliseconds: 200), () {
        Get.to(() =>
            storage.read('tempPincode') == null ? MapPage() : const RootPage());
      });
    } else {
      // Log failed registration
      InteractionLogger.logFormSubmit(
        formName: 'user_registration',
        success: false,
      );

      Get.back();
      customToast(message: 'Signup details invalid! try again.');
    }
  }

  void editProfile(params) async {
    buttonloading.value = true;
    log("Edit profile params: $params");

    // Log profile edit attempt
    InteractionLogger.logFormSubmit(
      formName: 'edit_profile',
      formData: params,
    );

    var user = await UserService.updateProfile(params);
    if (kDebugMode) {
      log('Edit profile response: $user');
    }

    if (user['success'] == true) {
      // Log successful profile edit
      InteractionLogger.logFormSubmit(
        formName: 'edit_profile',
        success: true,
      );

      loginstatus.value = true;
      storage.write('loginstatus', loginstatus.value);

      String? token = await FirebaseMessaging.instance.getToken();
      var req = {
        'user_id': userdata.value.data!.id.toString(),
        'user_firebase_token': token,
      };
      await UserService.saveDeviceToken(req);

      if (storage.read('locationaddress') != null) {
        locationaddress.value =
            Placemark.fromMap(storage.read('locationaddress'));
      }

      var userdtan = await UserService.getUserDetails({
        'user_id': userdata.value.data!.id.toString(),
      });

      if (userdtan?.data != null) {
        userdata.value = userdtan!;
        log(userdtan.toString());
      } else {
        buttonloading.value = false;
      }

      storage.write('userdata', UserData(data: userdtan?.data).toJson());

      if (userdata.value.data?.deleted == 1) {
        Future.delayed(const Duration(milliseconds: 500),
            (() => Get.to(() => const RootPage(data: true))));
        buttonloading.value = false;
        return;
      }

      Future.delayed(const Duration(milliseconds: 100), () {
        Get.to(() =>
            storage.read('tempPincode') == null ? MapPage() : const RootPage());
        buttonloading.value = false;
      });
    } else {
      // Log failed profile edit
      InteractionLogger.logFormSubmit(
        formName: 'edit_profile',
        success: false,
      );

      buttonloading.value = false;

      if (user['msg'] != null && user['msg'] != "") {
        customToast(message: user['msg']);
      } else {
        customToast(message: 'Signup details invalid! try again.');
      }
    }
  }

  void fetchUserWithEmail(params) async {
    // Log email login attempt
    InteractionLogger.logFormSubmit(
      formName: 'email_login',
      formData: {
        'email': params['email'],
        'password': '[REDACTED]',
      },
    );

    var user = await UserService.emailLogin(params);
    if (kDebugMode) {
      log('Email login response: $user');
    }

    if (user != null) {
      if (user.success == true) {
        // Log successful email login
        InteractionLogger.logFormSubmit(
          formName: 'email_login',
          success: true,
        );

        await storage.write('JWT', user.data?.token);
        log(storage.read('JWT'));

        storage.write('userdata', UserData(data: user.data).toJson());
        userdata.value = UserData(data: user.data);
        loginstatus.value = true;
        storage.write('loginstatus', loginstatus.value);

        String? token = await FirebaseMessaging.instance.getToken();
        var req = {
          'user_id': userdata.value.data!.id.toString(),
          'user_firebase_token': token,
        };
        await UserService.saveDeviceToken(req);

        if (storage.read('locationaddress') != null) {
          locationaddress.value =
              Placemark.fromMap(storage.read('locationaddress'));
        }

        await FirebaseService.firebaseAnalytics!.logEvent(
          name: 'login_user',
          parameters: {
            'name': userdata.value.data!.firstName!,
            'userid': userdata.value.data!.id!,
          },
        );
        await FirebaseService.firebaseAnalytics!.logLogin(loginMethod: "email");

        if (userdata.value.data?.deleted == 1) {
          Future.delayed(const Duration(milliseconds: 1000),
              (() => Get.to(() => const RootPage(data: true))));
          return;
        }

        Future.delayed(const Duration(seconds: 1), () {
          Get.to(() => storage.read('tempPincode') == null
              ? MapPage()
              : const RootPage());
        });
      } else {
        // Log failed email login
        InteractionLogger.logFormSubmit(
          formName: 'email_login',
          success: false,
        );

        Get.back();
        customToast(message: 'Login details invalid! try again.');
      }
    } else {
      // Log failed email login
      InteractionLogger.logFormSubmit(
        formName: 'email_login',
        success: false,
      );

      Get.back();
      customToast(message: 'Error');
    }
  }

  void googleSigninVerify(params) async {
    // Log Google signin attempt
    InteractionLogger.logFormSubmit(
      formName: 'google_signin',
      formData: {'google_token': '[REDACTED]'},
    );

    var user = await UserService.onGoogleSignin(params);
    if (kDebugMode) {
      log('Google signin response: $user');
    }

    if (user?.success == true) {
      // Log successful Google signin
      InteractionLogger.logFormSubmit(
        formName: 'google_signin',
        success: true,
      );

      await storage.write('JWT', user.data?.token);
      log(storage.read('JWT'));

      storage.write('userdata', UserData(data: user.data).toJson());
      userdata.value = UserData(data: user.data);
      loginstatus.value = true;
      storage.write('loginstatus', loginstatus.value);

      String? token = await FirebaseMessaging.instance.getToken();
      var req = {
        'user_id': userdata.value.data!.id.toString(),
        'user_firebase_token': token,
      };
      await UserService.saveDeviceToken(req);

      if (storage.read('locationaddress') != null) {
        locationaddress.value =
            Placemark.fromMap(storage.read('locationaddress'));
      }

      await FirebaseService.firebaseAnalytics!.logEvent(
        name: 'login_user',
        parameters: {
          'name': userdata.value.data!.firstName!,
          'userid': userdata.value.data!.id!,
        },
      );
      await FirebaseService.firebaseAnalytics!.logLogin(loginMethod: "google");

      if (userdata.value.data?.deleted == 1) {
        Future.delayed(const Duration(milliseconds: 1000),
            (() => Get.to(() => const RootPage(data: true))));
        return;
      }

      Future.delayed(const Duration(milliseconds: 10), () {
        Get.to(() =>
            storage.read('tempPincode') == null ? MapPage() : const RootPage());
      });
    } else {
      // Log failed Google signin
      InteractionLogger.logFormSubmit(
        formName: 'google_signin',
        success: false,
      );

      Get.back();
      customToast(message: 'Login details invalid! try again.');
    }
  }

  void verifyOtp(params) async {
  loading = true;
  update();

  // Log OTP verification attempt
  InteractionLogger.logFormSubmit(
    formName: 'otp_verification',
    formData: {
      'mobile': params['mobile'],
      'otp': params['otp']?.toString().replaceAll(RegExp(r'.'), '*'), // Mask OTP
    },
  );

  // Ensure params is Map<String, String>
  Map<String, String> otpParams = {};
  if (params is Map<String, dynamic>) {
    params.forEach((key, value) {
      otpParams[key] = value.toString();
    });
  } else if (params is Map<String, String>) {
    otpParams = params;
  } else {
    _logger.log(LogLevel.error, 'Invalid params type for verifyOtp: ${params.runtimeType}');
    loading = false;
    update();
    error = 'Invalid parameters for OTP verification';
    update();
    return;
  }

  var user = await UserService.verifyOtp(otpParams);
  if (kDebugMode) {
    log('OTP verification response: $user');
  }
  
  if (user != null) {
    if (user.success == true) {
      // Log successful OTP verification
      InteractionLogger.logFormSubmit(
        formName: 'otp_verification',
        success: true,
      );

      error = "";
      update();
      
      await storage.write('JWT', user.data?.token);
      log(storage.read('JWT'));

      storage.write('userdata', UserData(data: user.data).toJson());
      userdata.value = UserData(data: user.data);

      if (user.data?.is_newuser == 1) {
        // Log new user flow
        _logger.log(LogLevel.info, 'New user detected, navigating to personal details');
        
        Future.delayed(const Duration(milliseconds: 10), () {
          Get.offAll(() => PersonalDetailsScreen(
                userid: user.data?.id,
                phonenumber: params['mobile'],
              ));
        });
      } else {
        // Log existing user flow
        _logger.log(LogLevel.info, 'Existing user login successful');
        
        storage.write('userdata', UserData(data: user.data).toJson());
        userdata.value = UserData(data: user.data);
        loginstatus.value = true;
        storage.write('loginstatus', loginstatus.value);
        
        String? token = await FirebaseMessaging.instance.getToken();
        var req = {
          'user_id': userdata.value.data!.id.toString(),
          'user_firebase_token': token,
        };
        await UserService.saveDeviceToken(req);
        
        await FirebaseService.firebaseAnalytics!
            .setUserId(id: userdata.value.data!.id.toString())
            .then((value) => log('Firebase userId set'));

        UserService.getUserConfig("splash");
        
        if (storage.read('locationaddress') != null) {
          locationaddress.value = Placemark.fromMap(storage.read('locationaddress'));
        }

        if (userdata.value.data?.deleted == 1) {
          Future.delayed(
              const Duration(milliseconds: 100),
              (() => Get.to(() => const RootPage(data: true))));
          loading = false;
          update();
          return;
        }
        
        Future.delayed(const Duration(milliseconds: 100), () {
          Get.offAll(() => storage.read('tempPincode') == null ? MapPage() : const RootPage());
          loading = false;
          update();
        });
        
        await FirebaseService.firebaseAnalytics!.logEvent(
          name: 'login_user',
          parameters: {
            'name': userdata.value.data!.firstName!,
            'userid': userdata.value.data!.id!,
          },
        );
        await FirebaseService.firebaseAnalytics!.logLogin(loginMethod: "phone");
      }
    } else {
      // Log failed OTP verification
      InteractionLogger.logFormSubmit(
        formName: 'otp_verification',
        success: false,
      );
      
      log('OTP verification failed');
      loading = false;
      update();
      error = user.msg ?? 'OTP verification failed';
      update();
    }
  } else {
    // Log OTP verification error
    InteractionLogger.logFormSubmit(
      formName: 'otp_verification',
      success: false,
    );
    
    log('OTP verification error - null response');
    loading = false;
    update();
    error = 'OTP verification failed';
    update();
  }
}

void sendOtp(params) async {
  // Log OTP send attempt
  InteractionLogger.logFormSubmit(
    formName: 'send_otp',
    formData: {
      'mobile': params['mobile'],
    },
  );

  _logger.log(LogLevel.info, 'Sending OTP to: ${params['mobile']}');

  // Ensure params is Map<String, String>
  Map<String, String> otpParams = {};
  if (params is Map<String, dynamic>) {
    params.forEach((key, value) {
      otpParams[key] = value.toString();
    });
  } else if (params is Map<String, String>) {
    otpParams = params;
  } else {
    _logger.log(LogLevel.error, 'Invalid params type for sendOtp: ${params.runtimeType}');
    customToast(message: 'Invalid parameters for OTP request');
    return;
  }

  var user = await UserService.sendOTP(otpParams);

  if (kDebugMode) {
    log('Send OTP response: $user');
  }
  
  if (user?.success == true) {
    // Log successful OTP send
    InteractionLogger.logFormSubmit(
      formName: 'send_otp',
      success: true,
    );

    currentpin = user.data?.otp ?? '';
    _logger.log(LogLevel.info, 'OTP sent successfully');
  } else {
    // Log failed OTP send
    InteractionLogger.logFormSubmit(
      formName: 'send_otp',
      success: false,
    );
    
    customToast(message: 'Failed to send OTP! Please try again.');
    _logger.log(LogLevel.error, 'Failed to send OTP: ${user?.msg}');
  }
}

  @override
  void onInit() {
    loginstatus.value = false;
    _logger.log(LogLevel.info, 'UserController initialized');
    super.onInit();
  }

  @override
  void onClose() {
    _logger.log(LogLevel.info, 'UserController disposed');
    super.onClose();
  }
}
