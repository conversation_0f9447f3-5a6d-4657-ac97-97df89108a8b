import 'package:rapsap/view/widgets/commons.dart';
import 'package:pinput/pinput.dart';

class RootViewController extends GetxController {
  int _selectedIndex = 0;
  final ScrollController scrollController = ScrollController();

  int get selectedIndex => _selectedIndex;
  set selectedIndex(int val) {
    _selectedIndex = val;
    update();
  }

  void onScreenSelected(int index) {
    // if (_selectedIndex == 0) {
    //   scrollController.animateTo(
    //     scrollController.position.minScrollExtent,
    //     curve: Curves.easeOut,
    //     duration: const Duration(milliseconds: 700),
    //   );
    // }
    HapticFeedbackType.vibrate;
    selectedIndex = index;
    update();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }
}
