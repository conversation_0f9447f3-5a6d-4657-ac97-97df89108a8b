import 'dart:convert';

import 'datum.dart';
import 'meta.dart';

class CategoryProductModel {
  bool? success;
  String? msg;
  List<CategoryProducts>? data;
  Meta? meta;

  CategoryProductModel({this.success, this.msg, this.data, this.meta});

  @override
  String toString() {
    return 'CategoryProductModel(success: $success, msg: $msg, data: $data, meta: $meta)';
  }

  factory CategoryProductModel.fromMap(Map<String, dynamic> data) {
    return CategoryProductModel(
      success: data['success'] as bool?,
      msg: data['msg'] as String?,
      data: (data['data'] as List<dynamic>?)
          ?.map((e) => CategoryProducts.fromMap(e as Map<String, dynamic>))
          .toList(),
      meta: data['meta'] == null
          ? null
          : Meta.fromMap(data['meta'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toMap() => {
        'success': success,
        'msg': msg,
        'data': data?.map((e) => e.toMap()).toList(),
        'meta': meta?.toMap(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [CategoryProductModel].
  factory CategoryProductModel.fromJson(String data) {
    return CategoryProductModel.fromMap(
        json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [CategoryProductModel] to a JSON string.
  String toJson() => json.encode(toMap());
}
