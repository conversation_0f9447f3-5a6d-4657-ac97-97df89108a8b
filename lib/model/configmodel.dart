import 'package:flutter/foundation.dart';

@immutable
class ConfigModel {
  final int? id;
  final int? brandId;
  final int? outOfStock;
  final int? finishingSoon;
  final int? inStock;
  final int? plenty;
  final dynamic newInStock;
  final String? deliveryFee;
  final String? deliveryFeeThreshold;
  final String? minOrderValue;
  final String? createdAt;
  final String? updatedAt;
  final int? isavailable;

  const ConfigModel({
    this.isavailable,
    this.id,
    this.brandId,
    this.outOfStock,
    this.finishingSoon,
    this.inStock,
    this.plenty,
    this.newInStock,
    this.deliveryFee,
    this.deliveryFeeThreshold,
    this.minOrderValue,
    this.createdAt,
    this.updatedAt,
  });

  @override
  String toString() {
    return 'ConfigModel(id: $id, brandId: $brandId, outOfStock: $outOfStock, finishingSoon: $finishingSoon, inStock: $inStock, plenty: $plenty, newInStock: $newInStock, deliveryFee: $deliveryFee, deliveryFeeThreshold: $deliveryFeeThreshold, minOrderValue: $minOrderValue, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  factory ConfigModel.fromJson(Map<String, dynamic> json) => ConfigModel(
      id: json['id'] as int?,
      brandId: json['brand_id'] as int?,
      outOfStock: json['out_of_stock'] as int?,
      finishingSoon: json['finishing_soon'] as int?,
      inStock: json['in_stock'] as int?,
      plenty: json['plenty'] as int?,
      newInStock: json['new_in_stock'],
      deliveryFee: json['delivery_fee'] as String?,
      deliveryFeeThreshold: json['delivery_fee_threshold'] as String?,
      minOrderValue: json['min_order_value'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      isavailable: json['is_delivery_service_available'] as int?);

  Map<String, dynamic> toJson() => {
        'id': id,
        'brand_id': brandId,
        'out_of_stock': outOfStock,
        'finishing_soon': finishingSoon,
        'in_stock': inStock,
        'plenty': plenty,
        'new_in_stock': newInStock,
        'delivery_fee': deliveryFee,
        'delivery_fee_threshold': deliveryFeeThreshold,
        'min_order_value': minOrderValue,
        'created_at': createdAt,
        'updated_at': updatedAt,
        'is_delivery_service_available': isavailable
      };

  ConfigModel copyWith(
      {int? id,
      int? brandId,
      int? outOfStock,
      int? finishingSoon,
      int? inStock,
      int? plenty,
      dynamic newInStock,
      String? deliveryFee,
      String? deliveryFeeThreshold,
      String? minOrderValue,
      String? createdAt,
      String? updatedAt,
      int? isavailable}) {
    return ConfigModel(
        id: id ?? this.id,
        brandId: brandId ?? this.brandId,
        outOfStock: outOfStock ?? this.outOfStock,
        finishingSoon: finishingSoon ?? this.finishingSoon,
        inStock: inStock ?? this.inStock,
        plenty: plenty ?? this.plenty,
        newInStock: newInStock ?? this.newInStock,
        deliveryFee: deliveryFee ?? this.deliveryFee,
        deliveryFeeThreshold: deliveryFeeThreshold ?? this.deliveryFeeThreshold,
        minOrderValue: minOrderValue ?? this.minOrderValue,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        isavailable: isavailable ?? this.isavailable);
  }

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is ConfigModel &&
        other.id == id &&
        other.brandId == brandId &&
        other.outOfStock == outOfStock &&
        other.finishingSoon == finishingSoon &&
        other.inStock == inStock &&
        other.plenty == plenty &&
        other.newInStock == newInStock &&
        other.deliveryFee == deliveryFee &&
        other.deliveryFeeThreshold == deliveryFeeThreshold &&
        other.minOrderValue == minOrderValue &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.isavailable == isavailable;
  }

  @override
  int get hashCode =>
      id.hashCode ^
      brandId.hashCode ^
      outOfStock.hashCode ^
      finishingSoon.hashCode ^
      inStock.hashCode ^
      plenty.hashCode ^
      newInStock.hashCode ^
      deliveryFee.hashCode ^
      deliveryFeeThreshold.hashCode ^
      minOrderValue.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      isavailable.hashCode;
}
