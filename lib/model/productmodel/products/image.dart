import 'dart:convert';

class Image {
  String? url;
  int? imageId;

  Image({this.url, this.imageId});

  factory Image.fromMap(Map<String, dynamic> data) => Image(
        url: data['url'] as String?,
        imageId: data['image_id'] as int?,
      );

  Map<String, dynamic> toMap() => {
        'url': url,
        'image_id': imageId,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [Image].
  factory Image.fromJson(String data) {
    return Image.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [Image] to a JSON string.
  String toJson() => json.encode(toMap());
}
