import 'dart:convert';

import 'variant.dart';

class Data {
  int? productId;
  String? name;
  String? description;
  dynamic sku;
  int? isActive;
  dynamic meta;
  String? category;
  int? categoryId;
  int? subCategoryId;
  String? subCategoryName;
  String? brandName;
  dynamic subCategoryGroupId;
  dynamic subCategoryGroupName;
  List<Variant>? variants;
  String? isReviewed;
  int? isFavorite;
  String? avgRate;
  dynamic rating;
  int? ratingcount;
  bool? canreview;
  int? revieworderid;

  Data(
      {this.productId,
      this.name,
      this.description,
      this.sku,
      this.isActive,
      this.meta,
      this.category,
      this.categoryId,
      this.subCategoryId,
      this.subCategoryName,
      this.brandName,
      this.subCategoryGroupId,
      this.subCategoryGroupName,
      this.variants,
      this.isReviewed,
      this.isFavorite,
      this.avgRate,
      this.rating,
      this.canreview,
      this.ratingcount,
      this.revieworderid});

  factory Data.fromMap(Map<String, dynamic> data) => Data(
      productId: data['product_id'] as int?,
      name: data['name'] as String?,
      description: data['description'] as String?,
      sku: data['sku'] as dynamic,
      isActive: data['is_active'] as int?,
      meta: data['meta'] as dynamic,
      category: data['category'] as String?,
      categoryId: data['category_id'] as int?,
      subCategoryId: data['sub_category_id'] as int?,
      subCategoryName: data['sub_category_name'] as String?,
      brandName: data['brand_name'] as String?,
      subCategoryGroupId: data['sub_category_group_id'] as dynamic,
      subCategoryGroupName: data['sub_category_group_name'] as dynamic,
      variants: (data['variants'] as List<dynamic>?)
          ?.map((e) => Variant.fromMap(e as Map<String, dynamic>))
          .toList(),
      isReviewed: data['is_reviewed'] as String?,
      isFavorite: data['is_favorite'] as int?,
      avgRate: data['avg_rate'] as String?,
      rating: data['rating'] as dynamic,
      ratingcount: data['rating_count'] as int?,
      canreview: data['can_review'] == 1 ? true : false,
      revieworderid: data['review_order_id'] as int?);

  Map<String, dynamic> toMap() => {
        'product_id': productId,
        'name': name,
        'description': description,
        'sku': sku,
        'is_active': isActive,
        'meta': meta,
        'category': category,
        'category_id': categoryId,
        'sub_category_id': subCategoryId,
        'sub_category_name': subCategoryName,
        'brand_name': brandName,
        'sub_category_group_id': subCategoryGroupId,
        'sub_category_group_name': subCategoryGroupName,
        'variants': variants?.map((e) => e.toMap()).toList(),
        'is_reviewed': isReviewed,
        'is_favorite': isFavorite,
        'avg_rate': avgRate,
        'rating': rating,
        'rating_count': ratingcount,
        'can_review': canreview,
        'review_order_id': revieworderid
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [Data].
  factory Data.fromJson(String data) {
    return Data.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [Data] to a JSON string.
  String toJson() => json.encode(toMap());
}
