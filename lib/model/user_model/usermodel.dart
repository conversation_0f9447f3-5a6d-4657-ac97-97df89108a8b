import 'package:flutter/foundation.dart';

@immutable
class Data {
  final int? id;
  final String? firstName;
  final dynamic lastName;
  final String? email;
  final String? mobile;
  final dynamic googleUid;
  final String? firebaseId;
  final dynamic googleToken;
  final int? isActive;
  final dynamic gender;
  final dynamic dob;
  final int? roleId;
  final dynamic username;
  final dynamic userProfileImage;
  final dynamic refCode;
  final dynamic refUsedCode;
  final String? createAt;
  final String? updatedAt;
  final String? token;
  final String? otp;
  final dynamic is_newuser;

  final int? deleted;

  final String? deletestatus;

  const Data(
      {this.id,
      this.firstName,
      this.lastName,
      this.email,
      this.mobile,
      this.googleUid,
      this.firebaseId,
      this.googleToken,
      this.isActive,
      this.gender,
      this.dob,
      this.roleId,
      this.username,
      this.userProfileImage,
      this.refCode,
      this.refUsedCode,
      this.createAt,
      this.updatedAt,
      this.token,
      this.otp,
      this.is_newuser,
      this.deleted,
      this.deletestatus});

  @override
  String toString() {
    return 'Data(id: $id, firstName: $firstName, lastName: $lastName, email: $email, mobile: $mobile, googleUid: $googleUid, firebaseId: $firebaseId, googleToken: $googleToken, isActive: $isActive, gender: $gender, dob: $dob, roleId: $roleId, username: $username, userProfileImage: $userProfileImage, refCode: $refCode, refUsedCode: $refUsedCode, createAt: $createAt, updatedAt: $updatedAt, token: $token,otp: $otp,is_newuser:$is_newuser,is_deleted:$deleted,deletestatus:$deletestatus)';
  }

  factory Data.fromJson(Map<String, dynamic> json) => Data(
      id: json['id'] as int?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'],
      email: json['email'] as String?,
      mobile: json['mobile'] as String?,
      googleUid: json['google_uid'],
      firebaseId: json['firebase_id'] as String?,
      googleToken: json['google_token'],
      isActive: json['is_active'] as int?,
      gender: json['gender'],
      dob: json['dob'],
      roleId: json['role_id'] as int?,
      username: json['username'],
      userProfileImage: json['user_profile_image'],
      refCode: json['ref_code'],
      refUsedCode: json['ref_used_code'],
      createAt: json['create_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      token: json['token'] as String?,
      otp: json['otp'] as String?,
      is_newuser: json['is_newuser'],
      deleted: json['is_delete'],
      deletestatus: json['delete_status']);

  Map<String, dynamic> toJson() => {
        'id': id,
        'first_name': firstName,
        'last_name': lastName,
        'email': email,
        'mobile': mobile,
        'google_uid': googleUid,
        'firebase_id': firebaseId,
        'google_token': googleToken,
        'is_active': isActive,
        'gender': gender,
        'dob': dob,
        'role_id': roleId,
        'username': username,
        'user_profile_image': userProfileImage,
        'ref_code': refCode,
        'ref_used_code': refUsedCode,
        'create_at': createAt,
        'updated_at': updatedAt,
        'token': token,
        'otp': otp,
        'is_newuser': is_newuser,
        'is_delete': deleted,
        'delete_status': deletestatus
      };

  Data copyWith(
      {int? id,
      String? firstName,
      dynamic lastName,
      String? email,
      String? mobile,
      dynamic googleUid,
      String? firebaseId,
      dynamic googleToken,
      int? isActive,
      dynamic gender,
      dynamic dob,
      int? roleId,
      dynamic username,
      dynamic userProfileImage,
      dynamic refCode,
      dynamic refUsedCode,
      String? createAt,
      String? updatedAt,
      String? token,
      dynamic is_newuser,
      String? otp,
      String? deletestatus,
      int? deleted}) {
    return Data(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      mobile: mobile ?? this.mobile,
      googleUid: googleUid ?? this.googleUid,
      firebaseId: firebaseId ?? this.firebaseId,
      googleToken: googleToken ?? this.googleToken,
      isActive: isActive ?? this.isActive,
      gender: gender ?? this.gender,
      dob: dob ?? this.dob,
      roleId: roleId ?? this.roleId,
      username: username ?? this.username,
      userProfileImage: userProfileImage ?? this.userProfileImage,
      refCode: refCode ?? this.refCode,
      refUsedCode: refUsedCode ?? this.refUsedCode,
      createAt: createAt ?? this.createAt,
      updatedAt: updatedAt ?? this.updatedAt,
      token: token ?? this.token,
      otp: otp ?? this.otp,
      is_newuser: is_newuser ?? this.is_newuser,
      deletestatus: deletestatus ?? this.deletestatus,
      deleted: deleted ?? this.deleted,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is Data &&
        other.id == id &&
        other.firstName == firstName &&
        other.lastName == lastName &&
        other.email == email &&
        other.mobile == mobile &&
        other.googleUid == googleUid &&
        other.firebaseId == firebaseId &&
        other.googleToken == googleToken &&
        other.isActive == isActive &&
        other.gender == gender &&
        other.dob == dob &&
        other.roleId == roleId &&
        other.username == username &&
        other.userProfileImage == userProfileImage &&
        other.refCode == refCode &&
        other.refUsedCode == refUsedCode &&
        other.createAt == createAt &&
        other.updatedAt == updatedAt &&
        other.token == token;
  }

  @override
  int get hashCode =>
      id.hashCode ^
      firstName.hashCode ^
      lastName.hashCode ^
      email.hashCode ^
      mobile.hashCode ^
      googleUid.hashCode ^
      firebaseId.hashCode ^
      googleToken.hashCode ^
      isActive.hashCode ^
      gender.hashCode ^
      dob.hashCode ^
      roleId.hashCode ^
      username.hashCode ^
      userProfileImage.hashCode ^
      refCode.hashCode ^
      refUsedCode.hashCode ^
      createAt.hashCode ^
      updatedAt.hashCode ^
      token.hashCode;
}

class UserData {
  final bool? success;
  final String? msg;
  final Data? data;

  const UserData({this.success, this.msg, this.data});

  @override
  String toString() => 'UserData(success: $success, msg: $msg, data: $data)';

  factory UserData.fromJson(Map<String, dynamic> json) => UserData(
        success: json['success'] as bool?,
        msg: json['msg'] as String?,
        data: json['data'] == null
            ? null
            : Data.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'success': success,
        'msg': msg,
        'data': data?.toJson(),
      };

  UserData copyWith({
    bool? success,
    String? msg,
    Data? data,
  }) {
    return UserData(
      success: success ?? this.success,
      msg: msg ?? this.msg,
      data: data ?? this.data,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserData &&
        other.success == success &&
        other.msg == msg &&
        other.data == data;
  }

  @override
  int get hashCode => success.hashCode ^ msg.hashCode ^ data.hashCode;
}
