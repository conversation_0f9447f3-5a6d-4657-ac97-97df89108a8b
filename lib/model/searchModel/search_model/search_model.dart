import 'dart:convert';


import 'datum.dart';
import 'meta.dart';

class SearchModel {
  bool? success;
  String? msg;
  List<SearchResultItem>? data;
  Meta? meta;

  SearchModel({this.success, this.msg, this.data, this.meta});

  factory SearchModel.fromMap(Map<String, dynamic> data) => SearchModel(
        success: data['success'] as bool?,
        msg: data['msg'] as String?,
        data: (data['data'] as List<dynamic>?)
            ?.map((e) => SearchResultItem.fromMap(e as Map<String, dynamic>))
            .toList(),
        meta: data['meta'] == null
            ? null
            : Meta.fromMap(data['meta'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toMap() => {
        'success': success,
        'msg': msg,
        'data': data?.map((e) => e.toMap()).toList(),
        'meta': meta?.toMap(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [SearchModel].
  factory SearchModel.fromJson(String data) {
    return SearchModel.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [SearchModel] to a JSON string.
  String toJson() => json.encode(toMap());
}
