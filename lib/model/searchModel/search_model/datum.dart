import 'dart:convert';

class SearchResultItem {
  int? productId;
  String? name;
  int? isActive;
  dynamic isSubscription;
  int? categoryId;
  String? category;
  int? subCategoryId;
  String? subCategoryName;
  int? variantId;
  String? variantName;
  String? variantSku;
  String? price;
  String? costPrice;
  String? mrp;
  String? storingPrice;
  String? images;
  String? weight;

  SearchResultItem({
    this.productId,
    this.name,
    this.isActive,
    this.isSubscription,
    this.categoryId,
    this.category,
    this.subCategoryId,
    this.subCategoryName,
    this.variantId,
    this.variantName,
    this.variantSku,
    this.price,
    this.costPrice,
    this.mrp,
    this.storingPrice,
    this.images,
    this.weight,
  });

  factory SearchResultItem.fromMap(Map<String, dynamic> data) => SearchResultItem(
        productId: data['product_id'] as int?,
        name: data['name'] as String?,
        isActive: data['is_active'] as int?,
        isSubscription: data['is_subscription'] as dynamic,
        categoryId: data['category_id'] as int?,
        category: data['category'] as String?,
        subCategoryId: data['sub_category_id'] as int?,
        subCategoryName: data['sub_category_name'] as String?,
        variantId: data['variant_id'] as int?,
        variantName: data['variant_name'] as String?,
        variantSku: data['variant_sku'] as String?,
        price: double.parse(data['price'] ?? "0") == 0
            ? data['mrp']
            : data['price'] as String?,
        costPrice: data['cost_price'] as String?,
        mrp: data['mrp'] as String?,
        storingPrice: data['storing_price'] as String?,
        images: data['images'] as String?,
        weight: data['weight'] as String?,
      );

  Map<String, dynamic> toMap() => {
        'product_id': productId,
        'name': name,
        'is_active': isActive,
        'is_subscription': isSubscription,
        'category_id': categoryId,
        'category': category,
        'sub_category_id': subCategoryId,
        'sub_category_name': subCategoryName,
        'variant_id': variantId,
        'variant_name': variantName,
        'variant_sku': variantSku,
        'price': price,
        'cost_price': costPrice,
        'mrp': mrp,
        'storing_price': storingPrice,
        'images': images,
        'weight': weight,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [SearchResultItem].
  factory SearchResultItem.fromJson(String data) {
    return SearchResultItem.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [SearchResultItem] to a JSON string.
  String toJson() => json.encode(toMap());
}
