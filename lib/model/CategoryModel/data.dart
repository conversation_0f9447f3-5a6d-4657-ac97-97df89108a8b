class CategoryItemModel {
  CategoryItemModel(
      {this.categoryId,
      this.brandId,
      this.brandName,
      this.description,
      this.name,
      this.createdAt,
      this.updatedAt,
      this.categoryImage,
      this.imageId,
      this.bgcolor,
      this.txtcolor,
      this.totalproducts,
      this.bannerimage});

  int? categoryId;
  int? brandId;
  String? brandName;
  String? description;
  String? name;

  String? bgcolor;
  String? txtcolor;
  int? totalproducts;

  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic categoryImage;
  dynamic imageId;
  dynamic bannerimage;

  factory CategoryItemModel.fromJson(Map<String, dynamic> json) =>
      CategoryItemModel(
          categoryId: json["category_id"],
          brandId: json["brand_id"],
          brandName: json["brand_name"],
          description: json["description"],
          name: json["name"],
          bgcolor: json['bgcolor'],
          txtcolor: json['txtcolor'],
          createdAt: DateTime.parse(json["created_at"]),
          updatedAt: DateTime.parse(json["updated_at"]),
          categoryImage: json["category_image"],
          imageId: json["image_id"],
          totalproducts: json["total_products"],
          bannerimage: json['banner_image']);

  Map<String, dynamic> toJson() => {
        "category_id": categoryId,
        "brand_id": brandId,
        "brand_name": brandName,
        "description": description,
        "name": name,
        'bgcolor': bgcolor,
        'txtcolor': txtcolor,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "category_image": categoryImage,
        "image_id": imageId,
        "total_products": totalproducts,
        "banner_image": bannerimage
      };
}
