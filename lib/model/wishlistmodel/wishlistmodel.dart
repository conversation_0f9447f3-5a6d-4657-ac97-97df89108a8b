// To parse this JSON data, do
//
//     final wishlistModel = wishlistModelFromJson(jsonString?);

import 'dart:convert';

WishlistModel wishlistModelFromJson(String str) =>
    WishlistModel.fromJson(json.decode(str));

String? wishlistModelToJson(WishlistModel data) => json.encode(data.toJson());

class WishlistModel {
  WishlistModel({
    this.success,
    this.msg,
    this.data,
    this.meta,
  });

  bool? success;
  String? msg;
  List<WislistItems>? data;
  Meta? meta;

  factory WishlistModel.fromJson(Map<String?, dynamic> json) => WishlistModel(
        success: json["success"] == null ? null : json["success"],
        msg: json["msg"] == null ? null : json["msg"],
        data: json["data"] == null
            ? null
            : List<WislistItems>.from(
                json["data"].map((x) => WislistItems.fromJson(x))),
        meta: json["meta"] == null ? null : Meta.fromJson(json["meta"]),
      );

  Map<String?, dynamic> toJson() => {
        "success": success == null ? null : success,
        "msg": msg == null ? null : msg,
        "data": data == null
            ? null
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "meta": meta == null ? null : meta!.toJson(),
      };
}

class WislistItems {
  WislistItems({
    this.wishlistId,
    this.createdAt,
    this.name,
    this.description,
    this.productId,
    this.variantId,
    this.stock,
    this.weight,
    this.price,
    this.mrp,
    this.costPrice,
    this.image,
  });

  dynamic wishlistId;
  DateTime? createdAt;
  String? name;
  String? description;
  dynamic productId;
  dynamic variantId;
  dynamic stock;
  String? weight;
  String? price;
  String? mrp;
  String? costPrice;
  WishlistImage? image;

  factory WislistItems.fromJson(Map<String?, dynamic> json) => WislistItems(
        wishlistId: json["wishlist_id"] == null ? null : json["wishlist_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        name: json["name"] == null ? null : json["name"],
        description: json["description"] == null ? null : json["description"],
        productId: json["product_id"] == null ? null : json["product_id"],
        variantId: json["variant_id"] == null ? null : json["variant_id"],
        stock: json["stock"] == null ? null : json["stock"],
        weight: json["weight"] == null ? null : json["weight"],
        price: json["price"] == null
            ? null
            : json["price"] == "0.00"
                ? json["mrp"]
                : json['price'],
        mrp: json["mrp"] == null ? null : json["mrp"],
        costPrice: json["cost_price"] == null ? null : json["cost_price"],
        image: json["image"] == null
            ? null
            : WishlistImage.fromJson(json["image"]),
      );

  Map<String?, dynamic> toJson() => {
        "wishlist_id": wishlistId == null ? null : wishlistId,
        "created_at": createdAt == null ? null : createdAt!.toIso8601String(),
        "name": name == null ? null : name,
        "description": description == null ? null : description,
        "product_id": productId == null ? null : productId,
        "variant_id": variantId == null ? null : variantId,
        "stock": stock == null ? null : stock,
        "weight": weight == null ? null : weight,
        "price": price == null ? null : price,
        "mrp": mrp == null ? null : mrp,
        "cost_price": costPrice == null ? null : costPrice,
        "image": image == null ? null : image!.toJson(),
      };
}

class WishlistImage {
  WishlistImage({
    this.url,
    this.imageId,
  });

  String? url;
  dynamic imageId;

  factory WishlistImage.fromJson(Map<String?, dynamic> json) => WishlistImage(
        url: json["url"] == null ? null : json["url"],
        imageId: json["image_id"] == null ? null : json["image_id"],
      );

  Map<String?, dynamic> toJson() => {
        "url": url == null ? null : url,
        "image_id": imageId == null ? null : imageId,
      };
}

class Meta {
  Meta({
    this.total,
  });

  dynamic total;

  factory Meta.fromJson(Map<String?, dynamic> json) => Meta(
        total: json["total"] == null ? null : json["total"],
      );

  Map<String?, dynamic> toJson() => {
        "total": total == null ? null : total,
      };
}
