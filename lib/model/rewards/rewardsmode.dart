// To parse this JSON data, do
//
//     final rewards = rewardsFromJson(jsonString?);

import 'dart:convert';

List<Rewards> rewardsFromJson(String str) =>
    List<Rewards>.from(json.decode(str).map((x) => Rewards.fromJson(x)));

String? rewardsToJson(List<Rewards> data) =>
    json.encode(List<dynamic?>.from(data.map((x) => x.toJson())));

class Rewards {
  Rewards({
    this.offerId,
    this.id,
    this.name,
    this.description,
    this.couponCode,
    this.validFrom,
    this.validTill,
    this.isPercent,
    this.percentDiscount,
    this.amountDiscount,
    this.productId,
    this.categoryId,
    this.tagId,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.imageUrl,
    this.isActive,
  });

  int? offerId;
  int? id;
  String? name;
  String? description;
  String? couponCode;
  DateTime? validFrom;
  DateTime? validTill;
  int? isPercent;
  int? percentDiscount;
  String? amountDiscount;
  int? productId;
  int? categoryId;
  int? tagId;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic createdBy;
  dynamic updatedBy;
  String? imageUrl;
  int? isActive;

  factory Rewards.fromJson(Map<String?, dynamic> json) => Rewards(
        offerId: json["offer_id"],
        id: json["id"],
        name: json["name"],
        description: json["description"],
        couponCode: json["coupon_code"],
        validFrom: DateTime.parse(json["valid_from"]),
        validTill: DateTime.parse(json["valid_till"]),
        isPercent: json["is_percent"],
        percentDiscount:
            json["percent_discount"] == null ? null : json["percent_discount"],
        amountDiscount: json["amount_discount"],
        productId: json["product_id"] == null ? null : json["product_id"],
        categoryId: json["category_id"] == null ? null : json["category_id"],
        tagId: json["tag_id"],
        createdAt: DateTime.parse(json["created_at"]),
        updatedAt: DateTime.parse(json["updated_at"]),
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        imageUrl: json["image_url"] == null ? null : json["image_url"],
        isActive: json["is_active"],
      );

  Map<String?, dynamic?> toJson() => {
        "offer_id": offerId,
        "id": id,
        "name": name,
        "description": description,
        "coupon_code": couponCode,
        "valid_from": validFrom!.toIso8601String(),
        "valid_till": validTill!.toIso8601String(),
        "is_percent": isPercent,
        "percent_discount": percentDiscount == null ? null : percentDiscount,
        "amount_discount": amountDiscount,
        "product_id": productId == null ? null : productId,
        "category_id": categoryId == null ? null : categoryId,
        "tag_id": tagId,
        "created_at": createdAt!.toIso8601String(),
        "updated_at": updatedAt!.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
        "image_url": imageUrl == null ? null : imageUrl,
        "is_active": isActive,
      };
}
