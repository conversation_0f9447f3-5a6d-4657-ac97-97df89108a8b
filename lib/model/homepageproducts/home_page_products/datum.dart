import 'dart:convert';

import 'product.dart';

class HomePageCategoryData {
  int? categoryId;
  String? category;
  List<Product>? products;

  HomePageCategoryData({this.categoryId, this.category, this.products});

  factory HomePageCategoryData.fromMap(Map<String, dynamic> data) =>
      HomePageCategoryData(
        categoryId: data['category_id'] as int?,
        category: data['category'] as String?,
        products: (data['products'] as List<dynamic>?)
            ?.map((e) => Product.fromMap(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toMap() => {
        'category_id': categoryId,
        'category': category,
        'products': products?.map((e) => e.toMap()).toList(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [HomePageCategoryData].
  factory HomePageCategoryData.fromJson(String data) {
    return HomePageCategoryData.fromMap(
        json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [HomePageCategoryData] to a JSON string.
  String toJson() => json.encode(toMap());
}
