import 'dart:convert';

class Product {
  int? mrp;
  int? price;
  String? images;
  double? weight;
  String? product;
  String? category;
  int? costPrice;
  int? productId;
  int? variantId;
  int? categoryId;
  int? isFavorite;
  String? variantSku;
  String? variantName;
  int? subCategoryId;
  String? subCategoryName;

  Product({
    this.mrp,
    this.price,
    this.images,
    this.weight,
    this.product,
    this.category,
    this.costPrice,
    this.productId,
    this.variantId,
    this.categoryId,
    this.isFavorite,
    this.variantSku,
    this.variantName,
    this.subCategoryId,
    this.subCategoryName,
  });

  factory Product.fromMap(Map<String, dynamic> data) => Product(
        mrp: data['mrp'] as int?,
        price: data["price"] == 0 ? data["mrp"] : data['price'] as int?,
        images: data['images'] as String?,
        weight: double.parse(data['weight'].toString()),
        product: data['product'] as String?,
        category: data['category'] as String?,
        costPrice: data['cost_price'] as int?,
        productId: data['product_id'] as int?,
        variantId: data['variant_id'] as int?,
        categoryId: data['category_id'] as int?,
        isFavorite: data['is_favorite'] as int?,
        variantSku: data['variant_sku'] as String?,
        variantName: data['variant_name'] as String?,
        subCategoryId: data['sub_category_id'] as int?,
        subCategoryName: data['sub_category_name'] as String?,
      );

  Map<String, dynamic> toMap() => {
        'mrp': mrp,
        'price': price,
        'images': images,
        'weight': weight,
        'product': product,
        'category': category,
        'cost_price': costPrice,
        'product_id': productId,
        'variant_id': variantId,
        'category_id': categoryId,
        'is_favorite': isFavorite,
        'variant_sku': variantSku,
        'variant_name': variantName,
        'sub_category_id': subCategoryId,
        'sub_category_name': subCategoryName,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [Product].
  factory Product.fromJson(String data) {
    return Product.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [Product] to a JSON string.
  String toJson() => json.encode(toMap());
}
