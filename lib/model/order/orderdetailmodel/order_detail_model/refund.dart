// To parse this JSON data, do
//
//     final refund = refundFromMap(jsonString);

import 'dart:convert';

Refund? refundFromMap(String str) => Refund.fromMap(json.decode(str));

String refundToMap(Refund? data) => json.encode(data!.toMap());

class Refund {
  Refund({
    this.meta,
    this.reason,
    this.status,
    this.amount,
    this.refundId,
    this.updatedAt,
    this.refundType,
    this.referenceId,
  });

  Meta? meta;
  String? reason;
  dynamic amount;
  String? status;
  String? refundId;
  String? updatedAt;
  String? refundType;
  String? referenceId;

  factory Refund.fromMap(Map<String, dynamic> json) => Refund(
        meta: json['meta'] == null
            ? null
            : Meta.fromMap(json['meta'] as Map<String, dynamic>),
        reason: json["reason"],
        status: json["status"],
        amount: json["amount"],
        refundId: json["refund_id"],
        updatedAt: json["updated_at"],
        refundType: json["refund_type"],
        referenceId: json["reference_id"],
      );

  Map<String, dynamic> toMap() => {
        "meta": meta?.toMap(),
        "reason": reason,
        "status": status,
        "amount": amount,
        "refund_id": refundId,
        "updated_at": updatedAt,
        "refund_type": refundType,
        "reference_id": referenceId,
      };
}

class Meta {
  Meta({
    this.empty,
  });

  bool? empty;

  factory Meta.fromMap(Map<String, dynamic> json) => Meta(
        empty: json["empty"],
      );

  Map<String, dynamic> toMap() => {
        "empty": empty,
      };
}
