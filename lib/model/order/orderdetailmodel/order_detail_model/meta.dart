import 'dart:convert';

class Meta {
  Meta({
    this.extra,
    this.refType,
    this.dropEta,
    this.riderId,
    this.trackUrl,
    this.allotTime,
    this.pickupEta,
    this.riderName,
    this.orderStatus,
    this.sfxOrderId,
    this.riderContact,
    this.riderLatitude,
    this.clientOrderId,
    this.riderLongitude,
    this.arrivalTime,
    this.track,
    this.dispatchTime,
    this.customerDoorstepArrivalTime,
    this.returns,
    this.returnSkus,
    this.deliveryTime,
    this.dropImageUrl,
  });

  Extra? extra;
  String? refType;
  dynamic dropEta;
  dynamic riderId;
  String? trackUrl;
  DateTime? allotTime;
  dynamic pickupEta;
  String? riderName;
  String? orderStatus;
  dynamic sfxOrderId;
  String? riderContact;
  String? riderLatitude;
  dynamic clientOrderId;
  String? riderLongitude;
  DateTime? arrivalTime;
  String? track;
  DateTime? dispatchTime;
  DateTime? customerDoorstepArrivalTime;
  Returns? returns;
  List<dynamic>? returnSkus;
  DateTime? deliveryTime;
  dynamic dropImageUrl;
  factory Meta.fromJson(String data) {
    return Meta.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [Meta] to a JSON string.
  String toJson() => json.encode(toMap());

  factory Meta.fromMap(Map<String?, dynamic> json) => Meta(
        extra: json["extra"] == null ? null : Extra.fromJson(json["extra"]),
        refType: json["ref_type"] ?? '',
        dropEta: json["drop_eta"] ?? '',
        riderId: json["rider_id"] ?? '',
        trackUrl: json["track_url"] ?? '',
        allotTime: json["allot_time"],
        pickupEta: json["pickup_eta"] ?? '',
        riderName: json["rider_name"] ?? '',
        orderStatus: json["order_status"] ?? '',
        sfxOrderId: json["sfx_order_id"] ?? '',
        riderContact: json["rider_contact"] ?? '',
        riderLatitude: json["rider_latitude"] ?? '',
        clientOrderId: json["client_order_id"] ?? '',
        riderLongitude: json["rider_longitude"] ?? '',
        arrivalTime: json["arrival_time"],
        track: json["track"] ?? '',
        dispatchTime: json["dispatch_time"],
        customerDoorstepArrivalTime: json["customer_doorstep_arrival_time"],
        returns:
            json["returns"] == null ? null : Returns.fromJson(json["returns"]),
        returnSkus: json["return_skus"] == null
            ? null
            : List<dynamic>.from(json["return_skus"].map((x) => x)),
        deliveryTime: json["delivery_time"],
        dropImageUrl: json["drop_image_url"],
      );

  Map<String?, dynamic> toMap() => {
        "extra": extra == null ? null : extra!.toJson(),
        "ref_type": refType == null ? null : refType,
        "drop_eta": dropEta,
        "rider_id": riderId == null ? null : riderId,
        "track_url": trackUrl == null ? null : trackUrl,
        "allot_time": allotTime,
        "pickup_eta": pickupEta,
        "rider_name": riderName == null ? null : riderName,
        "order_status": orderStatus == null ? null : orderStatus,
        "sfx_order_id": sfxOrderId == null ? null : sfxOrderId,
        "rider_contact": riderContact == null ? null : riderContact,
        "rider_latitude": riderLatitude == null ? null : riderLatitude,
        "client_order_id": clientOrderId == null ? null : clientOrderId,
        "rider_longitude": riderLongitude == null ? null : riderLongitude,
        "arrival_time": arrivalTime,
        "track": track == null ? null : track,
        "dispatch_time": dispatchTime,
        "customer_doorstep_arrival_time": customerDoorstepArrivalTime,
        "returns": returns == null ? null : returns!.toJson(),
        "return_skus": returnSkus == null
            ? null
            : List<dynamic>.from(returnSkus!.map((x) => x)),
        "delivery_time": deliveryTime,
        "drop_image_url": dropImageUrl,
      };
}

class Extra {
  Extra({
    this.vpa,
    this.bank,
    this.card,
    this.email,
    this.notes,
    this.wallet,
    this.contact,
    this.errorCode,
    this.errorStep,
    this.errorReason,
    this.errorSource,
    this.errorDescription,
  });

  String? vpa;
  String? bank;
  String? card;
  String? email;
  Notes? notes;
  String? wallet;
  String? contact;
  String? errorCode;
  String? errorStep;
  String? errorReason;
  String? errorSource;
  String? errorDescription;

  factory Extra.fromJson(Map<String?, dynamic> json) => Extra(
        vpa: json["vpa"] ?? '',
        bank: json["bank"] ?? '',
        card: json["card"] ?? '',
        email: json["email"] ?? '',
        notes: json["notes"] == null ? null : Notes.fromJson(json["notes"]),
        wallet: json["wallet"] == null ? null : json["wallet"],
        contact: json["contact"] == null ? null : json["contact"],
        errorCode: json["error_code"] == null ? null : json["error_code"],
        errorStep: json["error_step"] == null ? null : json["error_step"],
        errorReason: json["error_reason"] == null ? null : json["error_reason"],
        errorSource: json["error_source"] == null ? null : json["error_source"],
        errorDescription: json["error_description"] == null
            ? null
            : json["error_description"],
      );

  Map<String?, dynamic> toJson() => {
        "vpa": vpa == null ? null : vpa,
        "bank": bank == null ? null : bank,
        "card": card == null ? null : card,
        "email": email == null ? null : email,
        "notes": notes == null ? null : notes!.toJson(),
        "wallet": wallet == null ? null : wallet,
        "contact": contact == null ? null : contact,
        "error_code": errorCode == null ? null : errorCode,
        "error_step": errorStep == null ? null : errorStep,
        "error_reason": errorReason == null ? null : errorReason,
        "error_source": errorSource == null ? null : errorSource,
        "error_description": errorDescription == null ? null : errorDescription,
      };
}

class Notes {
  Notes({
    this.userId,
    this.orderId,
    this.grandTotal,
  });

  String? userId;
  String? orderId;
  String? grandTotal;

  factory Notes.fromJson(Map<String?, dynamic> json) => Notes(
        userId: json["user_id"] ?? '',
        orderId: json["order_id"] ?? '',
        grandTotal: json["grand_total"] ?? '',
      );

  Map<String?, dynamic> toJson() => {
        "user_id": userId == null ? null : userId,
        "order_id": orderId == null ? null : orderId,
        "grand_total": grandTotal == null ? null : grandTotal,
      };
}

class Returns {
  Returns();

  factory Returns.fromJson(Map<String?, dynamic> json) => Returns();

  Map<String?, dynamic> toJson() => {};
}
