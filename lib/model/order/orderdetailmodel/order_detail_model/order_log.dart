import 'dart:convert';

import 'meta.dart';

class OrderLog {
  Meta? meta;
  String? reason;
  String? status;
  String? createdAt;
  int? orderLogId;
  dynamic referenceId;

  OrderLog({
    this.meta,
    this.reason,
    this.status,
    this.createdAt,
    this.orderLogId,
    this.referenceId,
  });

  factory OrderLog.fromMap(Map<String, dynamic> data) => OrderLog(
        meta: data['meta'] == null
            ? null
            : Meta.fromMap(data['meta'] as Map<String, dynamic>),
        reason: data['reason'] as String?,
        status: data['status'] as String?,
        createdAt: data['created_at'] as String?,
        orderLogId: data['order_log_id'] as int?,
        referenceId: data['reference_id'] as dynamic,
      );

  Map<String, dynamic> toMap() => {
        'meta': meta?.toMap(),
        'reason': reason,
        'status': status,
        'created_at': createdAt,
        'order_log_id': orderLogId,
        'reference_id': referenceId,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [OrderLog].
  factory OrderLog.fromJson(String data) {
    return OrderLog.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [OrderLog] to a JSON string.
  String toJson() => json.encode(toMap());
}
