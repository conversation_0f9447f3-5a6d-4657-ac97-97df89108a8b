import 'dart:convert';

class Meta {
  int? total;

  Meta({this.total});

  factory Meta.fromMap(Map<String, dynamic> data) => Meta(
        total: data['total'] as int?,
      );

  Map<String, dynamic> toMap() => {
        'total': total,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [Meta].
  factory Meta.fromJson(String data) {
    return Meta.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [Meta] to a JSON string.
  String toJson() => json.encode(toMap());
}
