import 'dart:convert';

import 'datum.dart';
import 'meta.dart';

class OrderListModel {
  bool? success;
  String? msg;
  List<Datum>? data;
  Meta? meta;

  OrderListModel({this.success, this.msg, this.data, this.meta});

  factory OrderListModel.fromMap(Map<String, dynamic> data) {
    return OrderListModel(
      success: data['success'] as bool?,
      msg: data['msg'] as String?,
      data: (data['data'] as List<dynamic>?)
          ?.map((e) => Datum.fromMap(e as Map<String, dynamic>))
          .toList(),
      meta: data['meta'] == null
          ? null
          : Meta.fromMap(data['meta'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toMap() => {
        'success': success,
        'msg': msg,
        'data': data?.map((e) => e.toMap()).toList(),
        'meta': meta?.toMap(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [OrderListModel].
  factory OrderListModel.fromJson(String data) {
    return OrderListModel.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [OrderListModel] to a JSON string.
  String toJson() => json.encode(toMap());
}
