import 'dart:convert';

import 'delivery.dart';
import 'order_detail.dart';

class Datum {
  int? orderId;
  String? orderStatus;
  int? offerId;
  DateTime? createdAt;
  String? orderType;
  dynamic name;
  dynamic mobile;
  String? brandName;
  String? paymentStatus;
  dynamic invoiceUrl;
  String? grandTotal;
  String? addressType;
  List<OrderDetail>? orderDetails;
  Delivery? delivery;

  Datum({
    this.orderId,
    this.orderStatus,
    this.offerId,
    this.createdAt,
    this.orderType,
    this.name,
    this.mobile,
    this.brandName,
    this.paymentStatus,
    this.invoiceUrl,
    this.grandTotal,
    this.addressType,
    this.orderDetails,
    this.delivery,
  });

  factory Datum.fromMap(Map<String, dynamic> data) => Datum(
        orderId: data['order_id'] as int?,
        orderStatus: data['order_status'] as String?,
        offerId: data['offer_id'] as int?,
        createdAt: data['created_at'] == null
            ? null
            : DateTime.parse(data['created_at'] as String),
        orderType: data['order_type'] as String?,
        name: data['name'] as dynamic,
        mobile: data['mobile'] as dynamic,
        brandName: data['brand_name'] as String?,
        paymentStatus: data['payment_status'] as String?,
        invoiceUrl: data['invoice_url'] as dynamic,
        grandTotal: data['grand_total'] as String?,
        addressType: data['address_type'] as String?,
        orderDetails: (data['OrderDetails'] as List<dynamic>?)
            ?.map((e) => OrderDetail.fromMap(e as Map<String, dynamic>))
            .toList(),
        delivery: data['delivery'] == null
            ? null
            : Delivery.fromMap(data['delivery'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toMap() => {
        'order_id': orderId,
        'order_status': orderStatus,
        'offer_id': offerId,
        'created_at': createdAt?.toIso8601String(),
        'order_type': orderType,
        'name': name,
        'mobile': mobile,
        'brand_name': brandName,
        'payment_status': paymentStatus,
        'invoice_url': invoiceUrl,
        'grand_total': grandTotal,
        'address_type': addressType,
        'OrderDetails': orderDetails?.map((e) => e.toMap()).toList(),
        'delivery': delivery?.toMap(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [Datum].
  factory Datum.fromJson(String data) {
    return Datum.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [Datum] to a JSON string.
  String toJson() => json.encode(toMap());
}
