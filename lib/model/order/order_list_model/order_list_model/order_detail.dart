import 'dart:convert';

import 'image.dart';

class OrderDetail {
  String? mrp;
  Image? image;
  String? price;
  int? quantity;
  String? costPrice;
  int? productId;
  int? variantId;
  String? buyingPrice;
  String? productName;
  int? orderDetailsId;

  OrderDetail({
    this.mrp,
    this.image,
    this.price,
    this.quantity,
    this.costPrice,
    this.productId,
    this.variantId,
    this.buyingPrice,
    this.productName,
    this.orderDetailsId,
  });

  factory OrderDetail.fromMap(Map<String, dynamic> data) => OrderDetail(
        mrp: data['mrp'] as String?,
        image: data['image'] == null
            ? null
            : Image.fromMap(data['image'] as Map<String, dynamic>),
        price: data['price'] as String?,
        quantity: data['quantity'] as int?,
        costPrice: data['cost_price'] as String?,
        productId: data['product_id'] as int?,
        variantId: data['variant_id'] as int?,
        buyingPrice: data['buying_price'] as String?,
        productName: data['product_name'] as String?,
        orderDetailsId: data['order_details_id'] as int?,
      );

  Map<String, dynamic> toMap() => {
        'mrp': mrp,
        'image': image?.toMap(),
        'price': price,
        'quantity': quantity,
        'cost_price': costPrice,
        'product_id': productId,
        'variant_id': variantId,
        'buying_price': buyingPrice,
        'product_name': productName,
        'order_details_id': orderDetailsId,
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [OrderDetail].
  factory OrderDetail.fromJson(String data) {
    return OrderDetail.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [OrderDetail] to a JSON string.
  String toJson() => json.encode(toMap());
}
