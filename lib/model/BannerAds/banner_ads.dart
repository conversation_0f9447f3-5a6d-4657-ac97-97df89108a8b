import 'datum.dart';
import 'meta.dart';

class BannerAds {
  bool? success;
  String? msg;
  List<BannerItem>? data;
  Meta? meta;

  BannerAds({this.success, this.msg, this.data, this.meta});

  factory BannerAds.fromJson(Map<String, dynamic> json) => BannerAds(
        success: json['success'] as bool?,
        msg: json['msg'] as String?,
        data: (json['data'] as List<dynamic>?)
            ?.map((e) => BannerItem.fromJson(e as Map<String, dynamic>))
            .toList(),
        meta: json['meta'] == null
            ? null
            : Meta.fromJson(json['meta'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'success': success,
        'msg': msg,
        'data': data?.map((e) => e.toJson()).toList(),
        'meta': meta?.toJson(),
      };
}
