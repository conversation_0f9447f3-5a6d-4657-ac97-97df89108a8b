class BannerItem {
  int? bannerId;
  int? id;
  dynamic name;
  dynamic description;
  String? type;
  dynamic url;
  int? productId;
  dynamic categoryId;
  dynamic subCategoryId;
  String? meta;
  dynamic validFrom;
  dynamic validTill;
  int? isActive;
  String? createdAt;
  String? updatedAt;
  int? bannerImageId;
  String? bannerImage;

  BannerItem({
    this.bannerId,
    this.id,
    this.name,
    this.description,
    this.type,
    this.url,
    this.productId,
    this.categoryId,
    this.subCategoryId,
    this.meta,
    this.validFrom,
    this.validTill,
    this.isActive,
    this.createdAt,
    this.updatedAt,
    this.bannerImageId,
    this.bannerImage,
  });

  factory BannerItem.fromJson(Map<String, dynamic> json) => BannerItem(
        bannerId: json['banner_id'] as int?,
        id: json['id'] as int?,
        name: json['name'] as dynamic?,
        description: json['description'] as dynamic?,
        type: json['type'] as String?,
        url: json['url'] as dynamic?,
        productId: json['product_id'] as int?,
        categoryId: json['category_id'] as dynamic?,
        subCategoryId: json['sub_category_id'] as dynamic?,
        meta: json['meta'] as String?,
        validFrom: json['valid_from'] as dynamic?,
        validTill: json['valid_till'] as dynamic?,
        isActive: json['is_active'] as int?,
        createdAt: json['created_at'] as String?,
        updatedAt: json['updated_at'] as String?,
        bannerImageId: json['banner_image_id'] as int?,
        bannerImage: json['banner_image']?.toString() ?? '',
      );

  Map<String, dynamic> toJson() => {
        'banner_id': bannerId,
        'id': id,
        'name': name,
        'description': description,
        'type': type,
        'url': url,
        'product_id': productId,
        'category_id': categoryId,
        'sub_category_id': subCategoryId,
        'meta': meta,
        'valid_from': validFrom,
        'valid_till': validTill,
        'is_active': isActive,
        'created_at': createdAt,
        'updated_at': updatedAt,
        'banner_image_id': bannerImageId,
        'banner_image': bannerImage,
      };
}
