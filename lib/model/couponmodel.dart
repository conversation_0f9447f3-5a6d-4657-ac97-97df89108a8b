// To parse this JSON data, do
//
//     final coupon = couponFromJson(jsonString);

import 'dart:convert';

List<Coupon> couponFromJson(String str) =>
    List<Coupon>.from(json.decode(str).map((x) => Coupon.fromJson(x)));

String couponToJson(List<Coupon> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Coupon {
  Coupon({
    this.id,
    this.name,
    this.description,
    this.couponCode,
    this.validFrom,
    this.validTill,
    this.isPercent,
    this.percentDiscount,
    this.amountDiscount,
    this.productId,
    this.variantId,
    this.categoryId,
    this.tagId,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.offerImage,
    this.offerImageId,
    this.variantName,
    this.categoryName,
    this.tagName,
    this.tagDescription,
    this.productImage,
    this.variantImage,
    this.categoryImage,
    this.productName,
  });

  dynamic id;
  String? name;
  String? description;
  String? couponCode;
  DateTime? validFrom;
  DateTime? validTill;
  dynamic isPercent;
  dynamic percentDiscount;
  dynamic amountDiscount;
  dynamic productId;
  dynamic variantId;
  dynamic categoryId;
  dynamic tagId;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic createdBy;
  dynamic updatedBy;
  String? offerImage;
  dynamic offerImageId;
  String? variantName;
  String? categoryName;
  String? tagName;
  String? tagDescription;
  String? productImage;
  String? variantImage;
  dynamic categoryImage;
  String? productName;

  factory Coupon.fromJson(Map<String, dynamic> json) => Coupon(
        id: json["id"] == null ? null : json["id"],
        name: json["name"] == null ? null : json["name"],
        description: json["description"] == null ? '' : json["description"],
        couponCode: json["coupon_code"] == null ? '' : json["coupon_code"],
        validFrom: json["valid_from"] == null
            ? null
            : DateTime.parse(json["valid_from"]),
        validTill: json["valid_till"] == null
            ? null
            : DateTime.parse(json["valid_till"]),
        isPercent: json["is_percent"] == null ? null : json["is_percent"],
        percentDiscount:
            json["percent_discount"] == null ? null : json["percent_discount"],
        amountDiscount:
            json["amount_discount"] == null ? null : json["amount_discount"],
        productId: json["product_id"] == null ? null : json["product_id"],
        variantId: json["variant_id"] == null ? null : json["variant_id"],
        categoryId: json["category_id"] == null ? null : json["category_id"],
        tagId: json["tag_id"] == null ? null : json["tag_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        offerImage: json["offer_image"] == null ? null : json["offer_image"],
        offerImageId:
            json["offer_image_id"] == null ? null : json["offer_image_id"],
        variantName: json["variant_name"] == null ? null : json["variant_name"],
        categoryName:
            json["category_name"] == null ? null : json["category_name"],
        tagName: json["tag_name"] == null ? null : json["tag_name"],
        tagDescription:
            json["tag_description"] == null ? null : json["tag_description"],
        productImage:
            json["product_image"] == null ? null : json["product_image"],
        variantImage:
            json["variant_image"] == null ? null : json["variant_image"],
        categoryImage: json["category_image"],
        productName: json["product_name"] == null ? null : json["product_name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "name": name == null ? null : name,
        "description": description == null ? '' : description,
        "coupon_code": couponCode == null ? '' : couponCode,
        "valid_from": validFrom == null ? null : validFrom!.toIso8601String(),
        "valid_till": validTill == null ? null : validTill!.toIso8601String(),
        "is_percent": isPercent == null ? null : isPercent,
        "percent_discount": percentDiscount == null ? null : percentDiscount,
        "amount_discount": amountDiscount == null ? null : amountDiscount,
        "product_id": productId == null ? null : productId,
        "variant_id": variantId == null ? null : variantId,
        "category_id": categoryId == null ? null : categoryId,
        "tag_id": tagId == null ? null : tagId,
        "created_at": createdAt == null ? null : createdAt!.toIso8601String(),
        "updated_at": updatedAt == null ? null : updatedAt!.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
        "offer_image": offerImage == null ? null : offerImage,
        "offer_image_id": offerImageId == null ? null : offerImageId,
        "variant_name": variantName == null ? null : variantName,
        "category_name": categoryName == null ? null : categoryName,
        "tag_name": tagName == null ? null : tagName,
        "tag_description": tagDescription == null ? null : tagDescription,
        "product_image": productImage == null ? null : productImage,
        "variant_image": variantImage == null ? null : variantImage,
        "category_image": categoryImage,
        "product_name": productName == null ? null : productName,
      };
}
