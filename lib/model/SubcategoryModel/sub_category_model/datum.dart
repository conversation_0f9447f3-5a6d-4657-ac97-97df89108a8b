import 'dart:convert';

class SubCategoryData {
  int? id;
  int? subCategoryId;
  int? categoryId;
  String? subCategoryName;
  dynamic description;
  String? categoryName;
  dynamic subCategoryImage;
  dynamic imageId;
  DateTime? createdAt;
  DateTime? updatedAt;

  dynamic bannerimage;

  SubCategoryData({
    this.id,
    this.subCategoryId,
    this.categoryId,
    this.subCategoryName,
    this.description,
    this.categoryName,
    this.subCategoryImage,
    this.imageId,
    this.createdAt,
    this.bannerimage,
    this.updatedAt,
  });

  factory SubCategoryData.fromMap(Map<String, dynamic> data) => SubCategoryData(
        id: data['id'] as int?,
        subCategoryId: data['sub_category_id'] as int?,
        categoryId: data['category_id'] as int?,
        subCategoryName: data['sub_category_name'] as String?,
        description: data['description'] as dynamic,
        categoryName: data['category_name'] as String?,
        subCategoryImage: data['sub_category_image'] as dynamic,
        imageId: data['image_id'] as dynamic,
        bannerimage: data['banner_image'] as dynamic,
        createdAt: data['created_at'] == null
            ? null
            : DateTime.parse(data['created_at'] as String),
        updatedAt: data['updated_at'] == null
            ? null
            : DateTime.parse(data['updated_at'] as String),
      );

  Map<String, dynamic> toMap() => {
        'id': id,
        'sub_category_id': subCategoryId,
        'category_id': categoryId,
        'sub_category_name': subCategoryName,
        'description': description,
        'category_name': categoryName,
        'sub_category_image': subCategoryImage,
        'image_id': imageId,
        'banner_image': bannerimage,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [SubCategoryData].
  factory SubCategoryData.fromJson(String data) {
    return SubCategoryData.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [SubCategoryData] to a JSON string.
  String toJson() => json.encode(toMap());
}
