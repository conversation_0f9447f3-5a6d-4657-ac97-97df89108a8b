import 'dart:convert';

import 'datum.dart';
import 'meta.dart';

class SubCategoryModel {
  bool? success;
  String? msg;
  List<SubCategoryData>? data;
  Meta? meta;

  SubCategoryModel({this.success, this.msg, this.data, this.meta});

  factory SubCategoryModel.fromMap(Map<String, dynamic> data) {
    return SubCategoryModel(
      success: data['success'] as bool?,
      msg: data['msg'] as String?,
      data: (data['data'] as List<dynamic>?)
          ?.map((e) => SubCategoryData.fromMap(e as Map<String, dynamic>))
          .toList(),
      meta: data['meta'] == null
          ? null
          : Meta.fromMap(data['meta'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toMap() => {
        'success': success,
        'msg': msg,
        'data': data?.map((e) => e.toMap()).toList(),
        'meta': meta?.toMap(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [SubCategoryModel].
  factory SubCategoryModel.fromJson(String data) {
    return SubCategoryModel.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [SubCategoryModel] to a JSON string.
  String toJson() => json.encode(toMap());
}
