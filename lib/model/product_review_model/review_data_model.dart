class ReviewDataModel {
  ReviewDataModel({
    this.rating,
    this.reviewId,
    this.description,
    this.userId,
    this.productId,
    this.orderId,
    this.createdAt,
    this.updatedAt,
    this.firstName,
    this.lastName,
    this.productName,
    this.brandName,
    this.userProfileImage,
  });

  int? rating;
  int? reviewId;
  String? description;
  int? userId;
  int? productId;
  int? orderId;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? firstName;
  dynamic lastName;
  String? productName;
  String? brandName;
  String? userProfileImage;

  factory ReviewDataModel.fromJson(Map<String, dynamic> json) =>
      ReviewDataModel(
        rating: json["rating"],
        reviewId: json["review_id"],
        description: json["description"],
        userId: json["user_id"],
        productId: json["product_id"],
        orderId: json["order_id"],
        createdAt: DateTime.parse(json["created_at"]),
        updatedAt: DateTime.parse(json["updated_at"]),
        firstName: json["first_name"],
        lastName: json["last_name"],
        productName: json["product_name"],
        brandName: json["brand_name"],
        userProfileImage: json["user_profile_image"],
      );

  Map<String, dynamic> toJson() => {
        "rating": rating,
        "review_id": reviewId,
        "description": description,
        "user_id": userId,
        "product_id": productId,
        "order_id": orderId,
        "created_at": createdAt!.toIso8601String(),
        "updated_at": updatedAt!.toIso8601String(),
        "first_name": firstName,
        "last_name": lastName,
        "product_name": productName,
        "brand_name": brandName,
        "user_profile_image": userProfileImage,
      };
}
