import 'dart:convert';

class Datum {
  int? addressId;
  String? name;
  String? addressType;
  int? isDefault;
  String? phone;
  String? address1;
  String? address2;
  String? landmark;
  String? city;
  String? state;
  String? pincode;
  String? longitude;
  String? latitude;
  int? userId;
  DateTime? createdAt;
  DateTime? updatedAt;

  Datum({
    this.addressId,
    this.name,
    this.addressType,
    this.isDefault,
    this.phone,
    this.address1,
    this.address2,
    this.landmark,
    this.city,
    this.state,
    this.pincode,
    this.longitude,
    this.latitude,
    this.userId,
    this.createdAt,
    this.updatedAt,
  });

  factory Datum.fromMap(Map<String, dynamic> data) => Datum(
        addressId: data['address_id'] as int?,
        name: data['name'] as String?,
        addressType: data['address_type'] as String?,
        isDefault: data['is_default'] as int?,
        phone: data['phone'] as String?,
        address1: data['address1'] as String?,
        address2: data['address2'] as String?,
        landmark: data['landmark'] as String?,
        city: data['city'] as String?,
        state: data['state'] as String?,
        pincode: data['pincode'] as String?,
        longitude: data['longitude'] as String?,
        latitude: data['latitude'] as String?,
        userId: data['user_id'] as int?,
        createdAt: data['created_at'] == null
            ? null
            : DateTime.parse(data['created_at'] as String),
        updatedAt: data['updated_at'] == null
            ? null
            : DateTime.parse(data['updated_at'] as String),
      );

  Map<String, dynamic> toMap() => {
        'address_id': addressId,
        'name': name,
        'address_type': addressType,
        'is_default': isDefault,
        'phone': phone,
        'address1': address1,
        'address2': address2,
        'landmark': landmark,
        'city': city,
        'state': state,
        'pincode': pincode,
        'longitude': longitude,
        'latitude': latitude,
        'user_id': userId,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [Datum].
  factory Datum.fromJson(String data) {
    return Datum.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [Datum] to a JSON string.
  String toJson() => json.encode(toMap());
}
