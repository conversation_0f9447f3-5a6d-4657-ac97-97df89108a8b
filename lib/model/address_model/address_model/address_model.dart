import 'dart:convert';

import 'datum.dart';

class AddressModel {
  bool? success;
  String? msg;
  List<Datum>? data;

  AddressModel({this.success, this.msg, this.data});

  factory AddressModel.fromMap(Map<String, dynamic> data) => AddressModel(
        success: data['success'] as bool?,
        msg: data['msg'] as String?,
        data: (data['data'] as List<dynamic>?)
            ?.map((e) => Datum.fromMap(e as Map<String, dynamic>))
            .toList(),
      );

  Map<String, dynamic> toMap() => {
        'success': success,
        'msg': msg,
        'data': data?.map((e) => e.toMap()).toList(),
      };

  /// `dart:convert`
  ///
  /// Parses the string and returns the resulting Json object as [AddressModel].
  factory AddressModel.fromJson(String data) {
    return AddressModel.fromMap(json.decode(data) as Map<String, dynamic>);
  }

  /// `dart:convert`
  ///
  /// Converts [AddressModel] to a JSON string.
  String toJson() => json.encode(toMap());
}
