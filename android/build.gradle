buildscript {
    ext.kotlin_version = '2.2.0'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        // classpath 'com.android.tools.build:gradle:8.0.2'  // Updated
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.2'  // Updated
        classpath 'com.google.firebase:perf-plugin:1.4.2'  // Updated
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'  // Updated
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
