<svg width="99" height="99" viewBox="0 0 99 99" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M73.8927 25.1023C87.3657 38.5754 87.3657 60.4196 73.8927 73.8927C60.4196 87.3657 38.5754 87.3657 25.1023 73.8927C11.6292 60.4196 11.6292 38.5754 25.1023 25.1023C38.5754 11.6292 60.4196 11.6292 73.8927 25.1023Z" fill="#E2E2E2" stroke="black"/>
<g filter="url(#filter0_d_3299_3647)">
<path d="M59.9297 38.2143L39.2295 58.9144" stroke="#FF5E5E" stroke-width="4" stroke-linecap="round"/>
</g>
<g filter="url(#filter1_d_3299_3647)">
<path d="M59.9297 59.0004L39.2295 38.3003" stroke="#FF5E5E" stroke-width="4" stroke-linecap="round"/>
</g>
<defs>
<filter id="filter0_d_3299_3647" x="27.2295" y="26.2143" width="44.7002" height="44.7001" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.368627 0 0 0 0 0.368627 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3299_3647"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3299_3647" result="shape"/>
</filter>
<filter id="filter1_d_3299_3647" x="27.2295" y="26.3003" width="44.7002" height="44.7001" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.368627 0 0 0 0 0.368627 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3299_3647"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3299_3647" result="shape"/>
</filter>
</defs>
</svg>
